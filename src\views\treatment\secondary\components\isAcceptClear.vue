<!--是否接受(清退)弹窗-->
<template>
  <BaseDialog
    title="是否接受（清退）"
    :visible.sync="dialogVisible"
    type="edit"
    :size="dialogSize"
    :close-on-click-modal="false"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="accept-form-container">
      <el-form ref="acceptForm" :model="formData" label-width="120px" class="accept-form">
        <!-- 是否接受选择 -->
        <el-form-item label="是否接受" prop="isAccept">
          <el-select
            v-model="formData.isAccept"
            placeholder="请选择"
            style="width: 200px"
            @change="handleAcceptChange"
          >
            <el-option label="否" value="否"></el-option>
            <el-option label="是" value="是"></el-option>
          </el-select>
        </el-form-item>

        <!-- 选择"否"时显示的字段 -->
        <template v-if="formData.isAccept === '否'">
          <el-form-item label="原因" prop="reason">
            <el-input
              v-model="formData.reason"
              type="textarea"
              :rows="4"
              placeholder="请输入原因"
              maxlength="200"
              show-word-limit
              resize="none"
            />
          </el-form-item>

          <el-form-item label="修改建议" prop="suggestion">
            <el-select
              v-model="formData.suggestion"
              placeholder="请选择"
              style="width: 200px"
            >
              <el-option label="保留" value="保留"></el-option>
              <el-option label="修改" value="修改"></el-option>
              <el-option label="删除" value="删除"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="上传证明材料" prop="attachments">
            <div class="upload-section">
              <el-button type="primary" @click="handleUpload">上传</el-button>
              <span v-if="formData.attachments.length > 0" class="file-info">
                {{ formData.attachments.map(f => f.name).join(', ') }}
              </span>
              <span v-else class="file-placeholder">xxx文件</span>
            </div>
          </el-form-item>
        </template>

        <!-- 选择"是"时显示的字段 -->
        <template v-if="formData.isAccept === '是'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="清退方式" prop="retirementMethod">
                <el-select
                  v-model="formData.retirementMethod"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option label="方式1" value="方式1"></el-option>
                  <el-option label="方式2" value="方式2"></el-option>
                  <el-option label="方式3" value="方式3"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="清退年份" prop="retirementYear">
                <el-select
                  v-model="formData.retirementYear"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option label="2025" value="2025"></el-option>
                  <el-option label="2024" value="2024"></el-option>
                  <el-option label="2023" value="2023"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="股权退出方案及对被投资企业公司合并报表产生的非经常性损益金额（万元）" prop="exitPlan">
                <el-input
                  v-model="formData.exitPlan"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入方案"
                  maxlength="300"
                  show-word-limit
                  resize="none"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="处置收益是否纳入本年度服务预算" prop="includeInBudget">
                <el-select
                  v-model="formData.includeInBudget"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option label="是" value="是"></el-option>
                  <el-option label="否" value="否"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="若计划需退出，但不能在年底完成，请简述原因（100字以内）" prop="delayReason">
            <el-input
              v-model="formData.delayReason"
              type="textarea"
              :rows="3"
              placeholder="请输入原因"
              maxlength="100"
              show-word-limit
              resize="none"
            />
          </el-form-item>
        </template>
      </el-form>
    </div>
  </BaseDialog>
</template>

<script>
import BaseDialog from '@/components/BaseDialog/index.vue'

export default {
  name: "isAccept",
  components: {
    BaseDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 初始数据
    initialData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formData: {
        isAccept: '',
        // 选择"否"时的字段
        reason: '',
        suggestion: '',
        attachments: [],
        // 选择"是"时的字段
        retirementYear: '',
        retirementMethod: '',
        includeInBudget: '',
        exitPlan: '',
        delayReason: ''
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    // 动态计算弹窗大小
    dialogSize() {
      return this.formData.isAccept === '是' ? 'Middle' : 'Small'
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initFormData()
      }
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      this.formData = {
        isAccept: this.initialData.isAccept || '',
        reason: this.initialData.reason || '',
        suggestion: this.initialData.suggestion || '',
        attachments: this.initialData.attachments || [],
        retirementYear: this.initialData.retirementYear || '',
        retirementMethod: this.initialData.retirementMethod || '',
        includeInBudget: this.initialData.includeInBudget || '',
        exitPlan: this.initialData.exitPlan || '',
        delayReason: this.initialData.delayReason || ''
      }
    },

    // 是否接受选择变化
    handleAcceptChange(value) {
      // 清空其他字段的值
      if (value === '否') {
        // 清空"是"相关的字段
        this.formData.retirementYear = ''
        this.formData.retirementMethod = ''
        this.formData.includeInBudget = ''
        this.formData.exitPlan = ''
        this.formData.delayReason = ''
      } else if (value === '是') {
        // 清空"否"相关的字段
        this.formData.reason = ''
        this.formData.suggestion = ''
        this.formData.attachments = []
      }
    },

    // 上传文件
    handleUpload() {
      this.$emit('upload-file')
      // 模拟文件上传
      this.formData.attachments.push({
        name: '证明材料.pdf',
        url: '#'
      })
      this.$message.success('文件上传成功')
    },

    // 确认提交
    handleConfirm() {
      this.$refs.acceptForm.validate((valid) => {
        if (valid) {
          // 验证必填项
          if (!this.formData.isAccept) {
            this.$message.warning('请选择是否接受')
            return
          }

          // 根据选择验证对应字段
          if (this.formData.isAccept === '否') {
            if (!this.formData.reason.trim()) {
              this.$message.warning('请输入原因')
              return
            }
          }

          this.$emit('confirm', { ...this.formData })
          this.dialogVisible = false
        }
      })
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.simple-form,
.complex-form {
  padding: 20px 0;
}

.accept-form .el-form-item {
  margin-bottom: 20px;
}

.accept-form .el-form-item:last-child {
  margin-bottom: 0;
}

.accept-form .el-textarea__inner {
  font-family: inherit;
  line-height: 1.5;
}

.accept-form .el-select {
  width: 100%;
}

/* 上传区域样式 */
.upload-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.file-info {
  color: #409eff;
  font-size: 14px;
}

.file-placeholder {
  color: #909399;
  font-size: 14px;
}

/* 复选框组样式 */
.accept-form .el-checkbox-group {
  display: flex;
  gap: 20px;
}

.accept-form .el-checkbox {
  margin-right: 0;
}

/* 选择框样式 */
.accept-form .el-select .el-input__inner {
  height: 36px;
  line-height: 36px;
}

/* 表单项标签样式 */
.accept-form .el-form-item__label {
  color: #606266;
  font-weight: bold;
}

/* 长标签特殊处理 */
.accept-form .el-form-item__label[for*="exitPlan"],
.accept-form .el-form-item__label[for*="delayReason"] {
  line-height: 1.4;
  white-space: normal;
  word-break: break-word;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .accept-form .el-col {
    margin-bottom: 15px;
  }

  .upload-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .accept-form .el-checkbox-group {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
