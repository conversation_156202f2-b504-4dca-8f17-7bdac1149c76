<template>
  <el-input 
    v-model="formModel[config.prop]"
    :type="config.type"
    :placeholder="config.placeholder"
    :clearable="config.clearable"
    :rows="config.rows"
    :readonly="config.readonly"
    :maxlength="config.maxlength"
    :minlength="config.minlength"
    :disabled="config.disabled"
    :style="config.style"
    @change="(e) => handleEvent(e, config.change)"
    @blur="(e) => handleEvent(e, config.blur)"
    @focus="(e) => handleEvent(e, config.focus)" />
</template>

<script>
  export default {
    name: "Input",
    props: {
      config: Object,
      formModel: Object
    },
    methods: {
      handleEvent(value, formEvent) {
        if(formEvent) {
          return formEvent(value)
        }
      }
    }
  }
</script>