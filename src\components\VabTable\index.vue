<template>
  <div>
    <el-table
      :id="idName"
      :data="tableData" 
      :border="isBorder"
      :stripe="isStripe"
      :height="tableHeight + 'px'"
      :header-cell-style="headerCellStyle"
      @selection-change="selectionChange"
      highlight-current-row
      @row-click="rowClick"
      :span-method="spanMethod"
      style="width: 100%"
      v-if="!isTreeTable"
    >
      <template #empty>
        <el-image
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
      <template v-for="(col,index) in tableHeader">
        <el-table-column 
          v-if="col.type == 'selection'" 
          :label="col.label" 
          :type="col.type" 
          :align="col.align" 
          :width="col.width" 
          :label-class-name="col.labelClassName" 
          :fixed="col.fixed" /> 
        <el-table-column 
          v-else-if="col.type == 'index'"
          :label="col.label" 
          :type="col.type" 
          :align="col.align" 
          :width="col.width" 
          label-class-name="number" 
          :fixed="col.fixed" />
        <!-- 操作 -->
        <el-table-column 
          v-else-if="col.type == 'action'" 
          :label="col.label" 
          :align="col.align" 
          :header-align="col.headerAlign"
          :width="col.width" 
          :fixed="col.fixed"
          :sort-method="sortMethodFun"
          :sortable="col.sortable">
          <template slot-scope="scope">
            <LesRender :scope="scope" :render="col.render" />
          </template>
        </el-table-column>
        <LesColumn v-else :col="col" :sort-method="sortMethodFun" :sortable="col.sortable"/>
      </template>
    </el-table>
    <el-table 
      :id="idName"
      :data="tableData" 
      :border="isBorder"
      :stripe="isStripe"
      :height="tableHeight + 'px'"
      :row-key="rowKey"
      :header-cell-style="headerCellStyle"
      highlight-current-row
      @selection-change="selectionChange"
      @row-click="rowClick"
      :rowKey="rowKey"
      style="width: 100%"
      :span-method="spanMethod"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      :default-expand-all="defaultExpandAll"
      v-else
    >
      <template #empty>
        <el-image
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
      <template v-for="(col,index) in tableHeader">
        <el-table-column 
          v-if="col.type == 'selection'" 
          :label="col.label" 
          :type="col.type" 
          :align="col.align" 
          :width="col.width" 
          :label-class-name="col.labelClassName" 
          :fixed="col.fixed" /> 
        <el-table-column 
          v-else-if="col.type == 'index' && !col.hiddenChildrenIndex" 
          :label="col.label" 
          :type="col.type" 
          :align="col.align" 
          :width="col.width" 
          label-class-name="number" 
          :fixed="col.fixed"/>
        
        <el-table-column
         :label="col.label" 
         :align="col.align" 
         :width="col.width" 
         :fixed="col.fixed"
         v-else-if="col.type == 'index' && col.hiddenChildrenIndex" 
        >
          <template slot-scope="scope">
            <!-- 通过计算判断是否为根节点 -->
            <span v-if="isRootNode(scope.row,col)">{{ getRootIndex(scope.$index,col) }}</span>
            <span v-else></span>
          </template>
        </el-table-column>
        <!-- 操作 -->
        <el-table-column 
          v-else-if="col.type == 'action'" 
          :label="col.label" 
          :align="col.align" 
          :header-align="col.headerAlign"
          :width="col.width" 
          :fixed="col.fixed"
          :sort-method="sortMethodFun"
          :sortable="col.sortable">
          <template slot-scope="scope">
            <LesRender :scope="scope" :render="col.render" />
          </template>
        </el-table-column>
        <LesColumn v-else :col="col" :sort-method="sortMethodFun" :sortable="col.sortable"/>
      </template>
    </el-table>
    <el-pagination
      v-if="showPagination"
      class="el-pagination-a"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pageNo"
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :layout="layout"
      :total="total">
    </el-pagination>
  </div>
</template>

<script>
  import LesRender from './LesRender'
  import LesColumn from './LesColumn'
  export default {
    name: "VabTable",
    components: {
      LesRender,
      LesColumn
    },
    props: {
      idName: {
        type: String,
        default: "",
      },
      tableData: {
        type: Array,
        required: true,
      },
      tableHeader: {
        type: Array,
        required: true,
      },
      isBorder: {
        type: Boolean,
        default() {
          return true
        }
      },
      isStripe: {
        type: Boolean,
        default() {
          return false
        }
      },
      tableHeight: {
        type: [Number,String],
        default: 500
      },
      selectionChange: {
        type: Function,
        default: () => {}
      },
      rowClick: {
        type: Function,
        default: () => {}
      },
      showPagination: {
        type: Boolean,
        default: true
      },
      layout: {
        type: String,
        default: 'total, sizes, prev, pager, next, jumper'
      },
      pageSize: {
        type: Number,
        default: 20
      },
      pageSizes: {
        type: Array,
        default: () => {
          return [20, 50, 100, 200, 500]
        }
      },
      pageNo: {
        type: Number,
        default: 1
      },
      total: {
        type: Number,
        default: 0
      },
      isTreeTable: {
        type: Boolean,
        default: false
      },
      rowKey: {
        type: String,
        default: 'id'
      },
      headerCellStyle: {
        type: [Function, Object]
      },
      spanMethod: {
        type: Function,
        default: () => {}
      },
      defaultExpandAll: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      // 计算属性：扁平化表格数据用于索引计算
      flattenTableData() {
        const result = [];
        
        const flatten = (data) => {
          data.forEach(row => {
            result.push(row);
            if (row.children && row.children.length) {
              flatten(row.children);
            }
          });
        };
        
        flatten(this.tableData);
        return result;
      }
    },
    methods: {
      handleSizeChange(val) {
        this.$emit('size-change',val)
      },
      handleCurrentChange(val) {
        this.$emit('current-change',val)
      },
      
      //排序
      sortMethodFun(a,b){
        return a-b;
      },
      // 判断是否为根节点（没有parentId属性或parentId为null）
      isRootNode(row,record) {
        if (record.mainValue) {
          return row[record.childrenIdName] === record.mainValue;
        } else {
          return row[record.childrenIdName] === undefined || row[record.childrenIdName] === null;
        }
      },
      
      // 获取根节点的真实索引（跳过所有子节点）
      getRootIndex(currentIndex,record) {
        let rootCount = 0;
        for (let i = 0; i <= currentIndex; i++) {
          const row = this.flattenTableData[i];
          if (this.isRootNode(row,record)) {
            rootCount++;
          }
        }
        return rootCount;
      }
    },
  }

</script>

<style lang="scss" scoped>
  ::v-deep .el-table__empty-block {
    width: 100%;
    min-width: 100%;
    max-width: 100%;
  }
</style>
