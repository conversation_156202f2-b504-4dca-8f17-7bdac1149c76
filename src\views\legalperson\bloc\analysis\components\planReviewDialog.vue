<!--计划审核弹窗-->
<template>
  <BaseDialog
    title="计划审核"
    :visible.sync="dialogVisible"
    type="edit"
    size="Small"
    :close-on-click-modal="false"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="plan-review-content">
      <!-- 查看计划按钮 -->
      <div class="view-plan-section">
        <el-button type="primary" @click="handleViewPlan">查看计划</el-button>
      </div>

      <el-form ref="planReviewForm" :model="formData" label-width="140px" class="plan-review-form">
        <!-- 处室主管审批 -->
        <div class="form-section">
          <el-form-item label="处室主管审批" prop="departmentApproval">
            <el-select
              v-model="formData.departmentApproval"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option label="同意" value="同意"></el-option>
              <el-option label="不同意" value="不同意"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="处室主管审批意见" prop="departmentOpinion">
            <el-input
              v-model="formData.departmentOpinion"
              type="textarea"
              :rows="3"
              placeholder="请输入处室主管审批意见"
              maxlength="200"
              show-word-limit
              resize="none"
            />
          </el-form-item>
        </div>

        <!-- 处室负责人审批 -->
        <div class="form-section">
          <el-form-item label="处室负责人审批" prop="directorApproval">
            <el-select
              v-model="formData.directorApproval"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option label="同意" value="同意"></el-option>
              <el-option label="不同意" value="不同意"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="处室负责人审批意见" prop="directorOpinion">
            <el-input
              v-model="formData.directorOpinion"
              type="textarea"
              :rows="3"
              placeholder="请输入处室负责人审批意见"
              maxlength="200"
              show-word-limit
              resize="none"
            />
          </el-form-item>
        </div>

        <!-- 经办人办理 -->
        <div class="form-section">
          <el-form-item label="经办人办理" prop="handlerProcess">
            <el-button type="primary" @click="handleSelectPlan">选择要回退的计划</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 选择回退计划弹窗 -->
    <BackDialog
      :visible.sync="backDialogVisible"
      @confirm="handleBackDialogConfirm"
      @suggestion="handleBackDialogSuggestion"
      @correction="handleBackDialogCorrection"
      @detail="handleBackDialogDetail"
    />

    <!-- 建议弹窗 -->
    <SuggestionDialog
      :visible.sync="suggestionDialogVisible"
      :initial-data="suggestionData"
      @confirm="handleSuggestionConfirm"
    />

    <!-- 修正弹窗 -->
    <CorrectDialog
      :visible.sync="correctDialogVisible"
      :initial-data="correctData"
      @confirm="handleCorrectConfirm"
    />
  </BaseDialog>
</template>

<script>
import BaseDialog from '@/components/BaseDialog/index.vue'
import BackDialog from './backDialog.vue'
import SuggestionDialog from './suggestionDialog.vue'
import CorrectDialog from './correctDialog.vue'

export default {
  name: "PlanReviewDialog",
  components: {
    BaseDialog,
    BackDialog,
    SuggestionDialog,
    CorrectDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 初始数据
    initialData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formData: {
        departmentApproval: '',
        departmentOpinion: '',
        directorApproval: '',
        directorOpinion: '',
        handlerProcess: '',
        selectedPlans: []
      },
      backDialogVisible: false,
      // 建议弹窗相关
      suggestionDialogVisible: false,
      suggestionData: {},
      // 修正弹窗相关
      correctDialogVisible: false,
      correctData: {}
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initFormData()
      }
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      this.formData = {
        departmentApproval: this.initialData.departmentApproval || '',
        departmentOpinion: this.initialData.departmentOpinion || '',
        directorApproval: this.initialData.directorApproval || '',
        directorOpinion: this.initialData.directorOpinion || '',
        handlerProcess: this.initialData.handlerProcess || '',
        selectedPlans: this.initialData.selectedPlans || []
      }
    },

    // 查看计划
    handleViewPlan() {
      this.$emit('view-plan')
      this.$message.info('查看计划功能')
    },

    // 选择要回退的计划
    handleSelectPlan() {
      this.backDialogVisible = true
    },

    // 处理选择回退计划弹窗确认
    handleBackDialogConfirm(selectedPlans) {
      console.log('选择的计划:', selectedPlans)
      this.$message.success(`已选择 ${selectedPlans.length} 个计划`)
      // 这里可以将选择的计划保存到当前表单数据中
      this.formData.selectedPlans = selectedPlans
      this.$emit('select-plan', selectedPlans)
    },

    // 处理回退计划弹窗中的建议操作
    handleBackDialogSuggestion(row) {
      console.log('回退计划建议:', row)
      this.suggestionData = { ...row }
      this.suggestionDialogVisible = true
    },

    // 处理回退计划弹窗中的修正操作
    handleBackDialogCorrection(row) {
      console.log('回退计划修正:', row)
      this.correctData = { ...row }
      this.correctDialogVisible = true
    },

    // 处理回退计划弹窗中的详情操作
    handleBackDialogDetail(row) {
      console.log('回退计划详情:', row)
      this.$message.info(`查看计划 ${row.planName} 的详情`)
      // 这里可以打开详情弹窗或执行其他操作
      this.$emit('detail', row)
    },

    // 处理建议弹窗确认
    handleSuggestionConfirm(formData) {
      console.log('建议数据:', formData)
      this.$message.success('建议提交成功')
      // 这里可以调用API提交建议数据
      this.$emit('suggestion-confirm', {
        planData: this.suggestionData,
        suggestion: formData.suggestion
      })
    },

    // 处理修正弹窗确认
    handleCorrectConfirm(formData) {
      console.log('修正数据:', formData)
      this.$message.success('修正提交成功')
      // 这里可以调用API提交修正数据
      this.$emit('correction-confirm', {
        planData: this.correctData,
        correctResult: formData.correctResult,
        isKeep: formData.isKeep
      })
    },

    // 确认提交
    handleConfirm() {
      this.$refs.planReviewForm.validate((valid) => {
        if (valid) {
          // 验证至少填写一个审批意见
          const hasApproval = this.formData.departmentApproval || this.formData.directorApproval
          if (!hasApproval) {
            this.$message.warning('请至少完成一项审批')
            return
          }

          this.$emit('confirm', { ...this.formData })
          this.dialogVisible = false
        }
      })
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.plan-review-content {
  padding: 10px 0;
}

.view-plan-section {
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.plan-review-form {
  margin-top: 20px;
}

.form-section {
  margin-bottom: 25px;
  padding: 15px;
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.form-section:last-child {
  margin-bottom: 0;
}

.plan-review-form .el-form-item {
  margin-bottom: 15px;
}

.plan-review-form .el-form-item:last-child {
  margin-bottom: 0;
}

.plan-review-form .el-textarea__inner {
  font-family: inherit;
  line-height: 1.5;
}

.plan-review-form .el-select {
  width: 100%;
}

/* 审批结果选择样式 */
.plan-review-form .el-select .el-input__inner {
  height: 36px;
  line-height: 36px;
}

/* 按钮样式调整 */
.view-plan-section .el-button {
  padding: 8px 20px;
}

.form-section .el-button {
  padding: 6px 15px;
}
</style>
