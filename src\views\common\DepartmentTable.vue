<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-left-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="100"
          @submit.native.prevent
        >
          <el-form-item>
            <el-input
              v-model="key"
              placeholder="快速搜索"
              @keyup.native="handleFind"
            ></el-input>
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-folder-opened"
              type="primary"
              @click="expandAllTreeEvent"
            >
              展开
            </el-button>
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-folder"
              type="primary"
              @click="claseExpandTreeEvent"
            >
              收缩
            </el-button>
          </el-form-item>

        </el-form>
      </vab-query-form-left-panel>
    </vab-query-form>

    <el-table
      class="mylist-table"
      v-loading="listLoading"
      ref="deptTable"
      id="dept"
      border
      stripe
      :height="height"
      :data="list"
      size="small"
      row-key="odId"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      @current-change="currentSelectRow"
      :default-expanded-keys="treeConfig.expandRowKeys"
    >
      <el-table-column 
        type="index" 
        width="70" 
        align="center" 
        fixed="left">
      </el-table-column>
      
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :label="item.label"
        :prop="item.prop"
        :align="item.align"
        header-align="center"
        :min-width="150"
      >
      </el-table-column>
      
      <template #empty>
        <el-image
          style="height: 110px"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>

  </div>
</template>

<script>
  import { getList } from '@/api/sysadmin/lesysDeptVue'
  
  export default {
    name: 'DepartmentTable',
    data() {
      return {
        isFullscreen: false,
        height: 500,
        list: [],
        tableData: [],
        listLoading: true,
        row: null,
        checkList: ['简称','全称'],
        columns: [
          { prop: "odName", label: "简称", sortable: false, align: 'left'},
          { prop: 'odFullname', label: '全称', sortable: false, align: 'center'},  
        ],
        treeConfig:{
          expandRowKeys: [], // 用于默认展开的行ID
        },
        key:'',
      }
    },
    computed: {
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    methods: {
      // 展开所有节点
      expandAllTreeEvent(){
        const $table = this.$refs.deptTable
        if ($table) {
          $table.toggleAllSelection()
          // 递归展开所有节点
          this.expandAllNodes(this.list, true)
        }
      },
      
      // 收起所有节点
      claseExpandTreeEvent(){
        const $table = this.$refs.deptTable
        if ($table) {
          // 递归收起所有节点
          this.expandAllNodes(this.list, false)
        }
      },
      
      // 递归展开/收起所有节点
      expandAllNodes(data, expanded) {
        data.forEach(node => {
          this.$refs.deptTable.toggleRowExpansion(node, expanded)
          if (node.children && node.children.length > 0) {
            this.expandAllNodes(node.children, expanded)
          }
        })
      },
      
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      
      // 快速查询
      handleFind() {
        let searchProps = []
        this.columns.forEach(key => {
          searchProps.push(key['prop'])
        })
        const filterVal = String(this.key).trim()
        if (filterVal) {
          const rest = this.tableData.filter(item => 
            searchProps.some(key => 
              String(item[key] || '').toLowerCase().includes(filterVal.toLowerCase())
            )
          )
          this.list = rest.map(row => {
            // 搜索为克隆数据，不会污染源数据
            return Object.assign({}, row)
          })
        } else {
          this.list = this.tableData
        }
      },
      
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        try {
          const data = await getList(this.searchForm)
          this.tableData = data.data
          this.list = data.data
          
          // 处理树形结构数据，确保children属性存在
          this.formatTreeData(this.list)
          
          // 默认展开第一个节点
          if(this.list.length > 0){
            this.treeConfig.expandRowKeys = [this.list[0].odId]
          }
        } catch (error) {
          console.error('获取数据失败:', error)
        } finally {
          this.listLoading = false
        }
      },
      
      // 格式化树形结构数据，确保children属性存在
      formatTreeData(data) {
        data.forEach(item => {
          if (!item.children) {
            item.children = []
          } else {
            this.formatTreeData(item.children)
          }
        })
      }
    },
  }
</script>
