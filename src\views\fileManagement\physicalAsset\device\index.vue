<!--设备资产管理页面-->
<template>
  <div class="custom-table-container">
    <!-- 顶部统计卡片 -->
    <div class="statistics-cards">
      <div class="stat-card">
        <div class="icon-wrapper blue-bg">
          <i class="el-icon-s-data" />
        </div>
        <div class="stat-info">
          <div class="stat-title">总设备数</div>
          <div class="stat-value blue-text">{{ statsData.totalCount || 0 }}<span>台</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper green-bg">
          <i class="el-icon-s-check" />
        </div>
        <div class="stat-info">
          <div class="stat-title">设备总价值</div>
          <div class="stat-value green-text">{{ statsData.normalCount || 0 }}<span>亿元</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper orange-bg">
          <i class="el-icon-warning" />
        </div>
        <div class="stat-info">
          <div class="stat-title">本年度处置项目数</div>
          <div class="stat-value orange-text">{{ statsData.idleCount || 0 }}<span>项</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper red-bg">
          <i class="el-icon-money" />
        </div>
        <div class="stat-info">
          <div class="stat-title">本年度租赁项目数</div>
          <div class="stat-value red-text">{{ statsData.totalValue || 0 }}<span>项</span></div>
        </div>
      </div>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" class="form-inline">
        <div class="leftRight">
          <div class="leftBar">
            <el-form-item label="资产名称:">
              <el-input v-model="searchForm.zcfaAssetsName" placeholder="请输入资产名称" class="inputW" />
            </el-form-item>
            <el-form-item label="产权单位:">
              <el-input v-model="searchForm.companyName" placeholder="请输入产权单位" class="inputW" />
            </el-form-item>
            <el-form-item label="资产状态:">
              <el-select v-model="searchForm.zcfaAssetsState" placeholder="请选择资产状态" class="inputW" clearable>
                <el-option label="正常" value="正常" />
                <el-option label="闲置" value="闲置" />
                <el-option label="报废" value="报废" />
                <el-option label="维修" value="维修" />
              </el-select>
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="handleAdvancedSearch" icon="el-icon-search">高级查询</el-button>
            <el-button @click="resetQuery" style="margin-right:10px">重置</el-button>
            <ExportButton :export-api="exportDevices" table-selector="#deviceTable" :query-form="searchForm" file-name="设备资产管理.xls" excel-title="设备资产管理" :date-fields="exportDateFields" :show-dropdown="true" :all-data-page-size="10000000" button-type="primary" :auto-exclude-operations="true" :exclude-columns="[]" :table-columns="tableColumns" @export-success="handleExportSuccess" @export-error="handleExportError" @export-all-success="handleExportAllSuccess" @export-all-error="handleExportAllError" />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table id="deviceTable" :data="list" :height="470" border stripe highlight-current-row style="width: 100%;" v-loading="listLoading" row-key="zcfaId">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="zcfaAssetsName" label="资产名称" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="companyName" label="产权单位" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zcfaAssetsState" label="资产状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getStatusTagType(scope.row.zcfaAssetsState)" size="small">
              {{ scope.row.zcfaAssetsState }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="zcfaType" label="资产类型" width="100" align="center" />
        <el-table-column prop="zcfaManufactor" label="制造厂商" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zcfaModel" label="型号" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zcfaSerialNumber" label="序号" width="100" align="center" />
        <el-table-column prop="zcfaBookValue" label="账面原值(万元)" width="130" align="center">
          <template slot-scope="scope">
            {{ scope.row.zcfaBookValue ? Number(scope.row.zcfaBookValue).toFixed(2) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="zcfaNetbookValue" label="账面净值(万元)" width="130" align="center">
          <template slot-scope="scope">
            {{ scope.row.zcfaNetbookValue ? Number(scope.row.zcfaNetbookValue).toFixed(2) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="zcfaProductDate" label="生产日期" width="110" align="center">
          <template slot-scope="scope">
            {{ scope.row.zcfaProductDate ? scope.row.zcfaProductDate.split(' ')[0] : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="zcfaRecordDate" label="入账日期" width="110" align="center">
          <template slot-scope="scope">
            {{ scope.row.zcfaRecordDate ? scope.row.zcfaRecordDate.split(' ')[0] : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="zcfaStoragePlace" label="存放地点" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zcfaSerialNum" label="资产编号" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zcfaCategory" label="分类" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zcfaUse" label="资产用途" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zcfaFundsSource" label="资金来源" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zcfaUsefulLife" label="折旧年限(年)" width="110" align="center" />
        <el-table-column prop="zcfaIfAssets" label="是否固定资产" width="110" align="center" />
        <el-table-column prop="zcfaContacts" label="联系人" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zcfaContactsPhone" label="联系电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zcfaRemark" label="备注" width="150" show-overflow-tooltip align="center" />
        <el-table-column label="操作" width="80" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination background class="el-pagination-a b-none" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 详情弹窗组件 -->
    <device-detail-dialog ref="deviceDetail" />

    <!-- 高级查询弹窗组件 -->
    <device-advanced-search-dialog ref="deviceAdvancedSearch" :external-search-form="searchForm" @search="handleAdvancedSearchSubmit" />
  </div>
</template>

<script>
import { getDevicesList, exportDevices, getDevicesStats } from '@/api/device'
import DeviceDetailDialog from './components/DeviceDetailDialog.vue'
import DeviceAdvancedSearchDialog from './components/DeviceAdvancedSearchDialog.vue'
import ExportButton from '@/components/ExportButton/index.vue'

export default {
  name: "index",
  components: {
    DeviceDetailDialog,
    DeviceAdvancedSearchDialog,
    ExportButton
  },
  data () {
    return {
      list: [],
      total: 0,
      listLoading: true,
      searchForm: {
        zcfaAssetsName: '',
        companyName: '',
        zcfaAssetsState: '',
        zcfaType: '',
        zcfaManufactor: '',
        zcfaModel: '',
        zcfaSerialNum: '',
        zcfaStoragePlace: '',
        zcfaUse: '',
        zcfaFundsSource: '',
        zcfaIfAssets: '',
        zcfaProductDateStart: '',
        zcfaProductDateEnd: '',
        zcfaRecordDateStart: '',
        zcfaRecordDateEnd: '',
        zcfaSerialNumber: '',
        zcfaBookValue: '',
        zcfaNetbookValue: '',
        zcfaCategory: '',
        zcfaUsefulLife: '',
        zcfaContacts: '',
        zcfaContactsPhone: '',
        zcfaRemark: '',
        pageNo: 1,
        pageSize: 10
      },
      productDateRange: [],
      recordDateRange: [],
      statsData: {
        totalCount: 0,
        normalCount: 0,
        idleCount: 0,
        totalValue: 0
      },

      // 表格列配置，用于导出
      tableColumns: [
        { prop: 'zcfaAssetsName', label: '资产名称' },
        { prop: 'companyName', label: '产权单位' },
        { prop: 'zcfaAssetsState', label: '资产状态' },
        { prop: 'zcfaType', label: '资产类型' },
        { prop: 'zcfaManufactor', label: '制造厂商' },
        { prop: 'zcfaModel', label: '型号' },
        { prop: 'zcfaSerialNumber', label: '序号' },
        { prop: 'zcfaBookValue', label: '账面原值(万元)' },
        { prop: 'zcfaNetbookValue', label: '账面净值(万元)' },
        { prop: 'zcfaProductDate', label: '生产日期' },
        { prop: 'zcfaRecordDate', label: '入账日期' },
        { prop: 'zcfaStoragePlace', label: '存放地点' },
        { prop: 'zcfaSerialNum', label: '资产编号' },
        { prop: 'zcfaCategory', label: '分类' },
        { prop: 'zcfaUse', label: '资产用途' },
        { prop: 'zcfaFundsSource', label: '资金来源' },
        { prop: 'zcfaUsefulLife', label: '折旧年限(年)' },
        { prop: 'zcfaIfAssets', label: '是否固定资产' },
        { prop: 'zcfaContacts', label: '联系人' },
        { prop: 'zcfaContactsPhone', label: '联系电话' },
        { prop: 'zcfaRemark', label: '备注' }
      ],

      // 导出日期字段配置
      exportDateFields: {
        zcfaProductDate: { celltype: "text" },
        zcfaRecordDate: { celltype: "text" }
      },

      // 导出API函数
      exportDevices
    }
  },
  created () {
    this.fetchData()
    this.fetchStatistics()
  },
  methods: {
    fetchData () {
      this.listLoading = true
      const query = {
        ...this.searchForm
      }

      getDevicesList(query).then(response => {
        if (response && response.data) {
          this.list = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.list = []
          this.total = 0
        }
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
        this.$message.error('获取数据失败')
      })
    },

    fetchStatistics () {
      getDevicesStats().then(response => {
        if (response && response.data) {
          this.statsData = {
            totalCount: response.data.TOTAL_FIXED_ASSETS_COUNT || 0,
            normalCount: response.data.TOTAL_FIXED_ASSETS_VALUE || 0,
            idleCount: response.data.idleCount || 0,
            totalValue: response.data.totalValue || 0
          }
        }
      }).catch(() => {
        this.$message.error('获取统计数据失败')
      })
    },

    onSearch () {
      this.searchForm.pageNo = 1
      this.fetchData()
    },
    resetQuery () {
      // 重置所有查询字段
      Object.keys(this.searchForm).forEach(key => {
        if (key !== 'pageNo' && key !== 'pageSize') {
          this.searchForm[key] = ''
        }
      })
      this.searchForm.pageNo = 1
      this.searchForm.pageSize = 10
      this.productDateRange = []
      this.recordDateRange = []
      this.fetchData()
    },
    handleProductDateChange (val) {
      if (val && val.length === 2) {
        this.searchForm.zcfaProductDateStart = val[0]
        this.searchForm.zcfaProductDateEnd = val[1]
      } else {
        this.searchForm.zcfaProductDateStart = ''
        this.searchForm.zcfaProductDateEnd = ''
      }
    },
    handleRecordDateChange (val) {
      if (val && val.length === 2) {
        this.searchForm.zcfaRecordDateStart = val[0]
        this.searchForm.zcfaRecordDateEnd = val[1]
      } else {
        this.searchForm.zcfaRecordDateStart = ''
        this.searchForm.zcfaRecordDateEnd = ''
      }
    },
    // 导出成功回调
    handleExportSuccess (response) {
      console.log('当前数据导出成功:', response)
      // 可以在这里添加额外的成功处理逻辑
    },

    // 导出失败回调
    handleExportError (error) {
      console.error('当前数据导出失败:', error)
      // 可以在这里添加额外的错误处理逻辑
    },

    // 全部数据导出成功回调
    handleExportAllSuccess (response) {
      console.log('全部数据导出成功:', response)
      // 可以在这里添加额外的成功处理逻辑
    },

    // 全部数据导出失败回调
    handleExportAllError (error) {
      console.error('全部数据导出失败:', error)
      // 可以在这里添加额外的错误处理逻辑
    },

    handleAdvancedSearch () {
      this.$refs.deviceAdvancedSearch.showDialog()
    },

    handleAdvancedSearchSubmit (searchParams) {
      // 直接使用高级查询的完整参数替换当前搜索表单
      this.searchForm = { ...searchParams }
      this.searchForm.pageNo = 1
      this.fetchData()
    },

    handleDetail (row) {
      this.$refs.deviceDetail.showDialog(row)
    },

    getStatusTagType (status) {
      const statusMap = {
        '正常': 'success',
        '闲置': 'warning',
        '报废': 'danger',
        '维修': 'info'
      }
      return statusMap[status] || 'info'
    },
    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
.leftRight {
  display: flex;
  justify-content: space-between;
}
.inputW {
  width: 250px;
}

.custom-table-container {
  padding: 16px;
  background-color: #f5f7fa;
}

/* 顶部统计卡片样式 */
.statistics-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-right: 15px;
}

.stat-card:last-child {
  margin-right: 0;
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.icon-wrapper i {
  font-size: 24px;
  color: #fff;
}

.red-bg {
  background-color: rgba(245, 108, 108, 0.2);
}

.orange-bg {
  background-color: rgba(230, 162, 60, 0.2);
}

.blue-bg {
  background-color: rgba(64, 158, 255, 0.2);
}

.green-bg {
  background-color: rgba(103, 194, 58, 0.2);
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
}

.stat-value span {
  font-size: 14px;
  margin-left: 4px;
}

.red-text {
  color: #f56c6c;
}

.orange-text {
  color: #e6a23c;
}

.blue-text {
  color: #409eff;
}

.green-text {
  color: #67c23a;
}

/* 搜索表单样式 */
.search-form {
  background-color: #fff;
  padding: 20px 20px 0 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

/* 数据表格样式 */
.table-section {
  background: white;
  border-radius: 4px;
}
</style>
