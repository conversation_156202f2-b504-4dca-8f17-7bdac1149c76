<!--是否接受(整改)弹窗-->
<template>
  <BaseDialog
    title="是否接受（整改）"
    :visible.sync="dialogVisible"
    type="edit"
    :size="dialogSize"
    :close-on-click-modal="false"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="accept-correction-container">
      <el-form ref="acceptForm" :model="formData" label-width="100px" class="accept-form">
        <!-- 是否接受选择 -->
        <el-form-item label="是否接受" prop="isAccept">
          <el-select
            v-model="formData.isAccept"
            placeholder="请选择"
            style="width: 200px"
            @change="handleAcceptChange"
          >
            <el-option label="是" value="是"></el-option>
            <el-option label="否" value="否"></el-option>
          </el-select>
        </el-form-item>

        <!-- 选择"是"时显示的字段 -->
        <template v-if="formData.isAccept === '是'">
          <el-form-item label="整改方式" prop="correctionMethod">
            <el-select
              v-model="formData.correctionMethod"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option label="方式1" value="方式1"></el-option>
              <el-option label="方式2" value="方式2"></el-option>
              <el-option label="方式3" value="方式3"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="整改方案" prop="correctionPlan">
            <el-input
              v-model="formData.correctionPlan"
              type="textarea"
              :rows="4"
              placeholder="请输入整改方案"
              maxlength="500"
              show-word-limit
              resize="none"
            />
          </el-form-item>

          <el-form-item label="完成时间" prop="completionTime">
            <el-date-picker
              v-model="formData.completionTime"
              type="date"
              placeholder="请选择完成时间"
              style="width: 100%"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
        </template>

        <!-- 选择"否"时显示的字段 -->
        <template v-if="formData.isAccept === '否'">
          <el-form-item label="原因" prop="reason">
            <el-input
              v-model="formData.reason"
              type="textarea"
              :rows="4"
              placeholder="请输入原因"
              maxlength="300"
              show-word-limit
              resize="none"
            />
          </el-form-item>

          <el-form-item label="依据" prop="basis">
            <el-input
              v-model="formData.basis"
              type="textarea"
              :rows="4"
              placeholder="请输入依据"
              maxlength="300"
              show-word-limit
              resize="none"
            />
          </el-form-item>

          <el-form-item label="上传证明材料" prop="attachments">
            <div class="upload-section">
              <el-button type="primary" @click="handleUpload">上传</el-button>
              <span v-if="formData.attachments.length > 0" class="file-info">
                {{ formData.attachments.map(f => f.name).join(', ') }}
              </span>
              <span v-else class="file-placeholder">xxx文件</span>
            </div>
          </el-form-item>
        </template>
      </el-form>
    </div>
  </BaseDialog>
</template>

<script>
import BaseDialog from '@/components/BaseDialog/index.vue'

export default {
  name: "isAcceptCorrection",
  components: {
    BaseDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 初始数据
    initialData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formData: {
        isAccept: '',
        // 选择"是"时的字段
        correctionMethod: '',
        correctionPlan: '',
        completionTime: '',
        // 选择"否"时的字段
        reason: '',
        basis: '',
        attachments: []
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    // 动态计算弹窗大小
    dialogSize() {
      return 'Small'
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initFormData()
      }
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      this.formData = {
        isAccept: this.initialData.isAccept || '',
        correctionMethod: this.initialData.correctionMethod || '',
        correctionPlan: this.initialData.correctionPlan || '',
        completionTime: this.initialData.completionTime || '',
        reason: this.initialData.reason || '',
        basis: this.initialData.basis || '',
        attachments: this.initialData.attachments || []
      }
    },

    // 是否接受选择变化
    handleAcceptChange(value) {
      // 清空其他字段的值
      if (value === '是') {
        // 清空"否"相关的字段
        this.formData.reason = ''
        this.formData.basis = ''
        this.formData.attachments = []
      } else if (value === '否') {
        // 清空"是"相关的字段
        this.formData.correctionMethod = ''
        this.formData.correctionPlan = ''
        this.formData.completionTime = ''
      }
    },

    // 上传文件
    handleUpload() {
      this.$emit('upload-file')
      // 模拟文件上传
      this.formData.attachments.push({
        name: '证明材料.pdf',
        url: '#'
      })
      this.$message.success('文件上传成功')
    },

    // 确认提交
    handleConfirm() {
      this.$refs.acceptForm.validate((valid) => {
        if (valid) {
          // 验证必填项
          if (!this.formData.isAccept) {
            this.$message.warning('请选择是否接受')
            return
          }

          // 根据选择验证对应字段
          if (this.formData.isAccept === '是') {
            if (!this.formData.correctionMethod) {
              this.$message.warning('请选择整改方式')
              return
            }
            if (!this.formData.correctionPlan.trim()) {
              this.$message.warning('请输入整改方案')
              return
            }
            if (!this.formData.completionTime) {
              this.$message.warning('请选择完成时间')
              return
            }
          } else if (this.formData.isAccept === '否') {
            if (!this.formData.reason.trim()) {
              this.$message.warning('请输入原因')
              return
            }
            if (!this.formData.basis.trim()) {
              this.$message.warning('请输入依据')
              return
            }
          }

          this.$emit('confirm', { ...this.formData })
          this.dialogVisible = false
        }
      })
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.accept-correction-container {
  padding: 20px 0;
}

.accept-form .el-form-item {
  margin-bottom: 20px;
}

.accept-form .el-form-item:last-child {
  margin-bottom: 0;
}

.accept-form .el-textarea__inner {
  font-family: inherit;
  line-height: 1.5;
}

.accept-form .el-select {
  width: 100%;
}

/* 上传区域样式 */
.upload-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.file-info {
  color: #409eff;
  font-size: 14px;
}

.file-placeholder {
  color: #909399;
  font-size: 14px;
}

/* 选择框样式 */
.accept-form .el-select .el-input__inner {
  height: 36px;
  line-height: 36px;
}

/* 表单项标签样式 */
.accept-form .el-form-item__label {
  color: #606266;
  font-weight: bold;
}

/* 日期选择器样式 */
.accept-form .el-date-editor {
  width: 100%;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .upload-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
