<template>
  <div class="custom-table-container">
    <div class="search-container">
      <div>
        <el-button type="primary" @click="handleRectificationPlan">推送年度参股企业清退计划</el-button>
        <el-button type="primary">上报国资委</el-button>
        <el-button type="primary">纳入下一清退改计划</el-button>
      </div>
      <el-button type="primary">综合查询</el-button>
    </div>
    <div class="table-section">
      <el-table :data="tableData" :height="height" border stripe highlight-current-row style="width: 100%;" v-loading="loading" row-key="zchiId">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="zchiAssetsNo" label="资产编号" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiAssetsName" label="资产名称" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiUseDescribe" label="现状/用途" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="companyName" label="产权单位" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCertificateCode" label="产权证号" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiAddress" label="地理位置" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiHouseSource" label="取得方式" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDate" label="取得时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiArea" label="建筑面积(㎡)" width="120" align="center" />
        <el-table-column prop="zchiOriginalValue" label="原值(万元)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiNetValue" label="净值(万元)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiTotalDepreciation" label="累计折旧(万元)" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCountry" label="境内/境外" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIfAssets" label="是否两非资产" width="120" align="center" />
        <el-table-column prop="zchiIfExist" label="是否取得房屋产权证" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIfDispute" label="是否存在纠纷" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIfDispose" label="是否可处置" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIfMortgage" label="是否存在抵押" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiOperator" label="联系人" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiOperatorTel" label="联系电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiProvince" label="省份" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCity" label="城市" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCounty" label="区/县" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDeptName" label="业务管理部门" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDepartmentLeader" label="部门负责人" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDepartmentTel" label="部门电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCompanyLeader" label="分公司负责人" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCompanyTel" label="分公司电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiEvaluateValue" label="评估价值(万元)" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiEvaluateDate" label="评估日期" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiServiceLife" label="使用年限(年)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDepreciableYear" label="计提年限" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiOfficeArea" label="科研办公面积" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCommercialArea" label="商业面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiResidentialArea" label="住宅面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIndustrialArea" label="工业面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiUndergroundArea" label="地下建筑面积" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiOtherArea" label="其他面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiRemark" label="备注" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="createdTime" label="创建时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="createdBy" label="创建人" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="updatedTime" label="更新时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column label="操作" width="120" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
            <el-button type="text" size="small" @click="handleProgress(scope.row)">进展情况</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination background class="el-pagination-a" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 推送年度参股企业清退计划弹窗 -->
    <RectificationPlan :visible.sync="rectificationPlanVisible" :initial-data="rectificationPlanData" @confirm="handleRectificationPlanConfirm" />

    <!-- 进展情况弹窗 -->
    <ProgressDialog :visible.sync="progressDialogVisible" :current-step="currentProgressStep" :progress-data="currentProgressData" @view-plan="handleViewPlan" @view-attachment="handleViewAttachment" />
  </div>
</template>

<script>
import { getBuildingsList } from '@/api/buildings'
import RectificationPlan from './components/rectificationPlan.vue'
import ProgressDialog from './components/progressDialog.vue'

export default {
  components: {
    RectificationPlan,
    ProgressDialog
  },
  data () {
    return {
      loading: false,
      height: this.$baseTableHeight(1, 1),
      searchForm: {
        pageNo: 1,
        pageSize: 20
      },
      tableData: [],
      total: 0,
      // 推送年度参股企业清退计划弹窗相关
      rectificationPlanVisible: false,
      rectificationPlanData: {},
      // 进展情况弹窗相关
      progressDialogVisible: false,
      currentProgressStep: 1,
      currentProgressData: {},
      currentRowData: null
    }
  },

  created () {
    this.fetchData()
  },
  methods: {
    fetchData () {
      this.loading = true
      getBuildingsList(this.searchForm).then(response => {
        if (response && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.tableData = []
          this.total = 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取数据失败')
      })
    },

    handleDetail (row) {
      console.log('查看详情:', row)
    },

    // 推送年度参股企业清退计划
    handleRectificationPlan () {
      this.rectificationPlanData = {}
      this.rectificationPlanVisible = true
    },

    // 推送年度参股企业清退计划确认
    handleRectificationPlanConfirm (formData) {
      console.log('推送年度参股企业清退计划提交:', formData)
      // 这里可以调用API提交数据
      this.$message.success('推送成功')
    },

    // 查看进展情况
    handleProgress (row) {
      this.currentRowData = row
      // 根据实际业务逻辑设置当前步骤和进展数据
      this.currentProgressStep = 2 // 示例：当前在第2步
      this.currentProgressData = {
        step1: {
          handler: '张三',
          department: '资产管理部',
          info: '已完成年度参股企业清退计划的制定和上报工作'
        },
        step2: {
          departmentOpinions: [
            '计划内容详实，建议通过-2025-08-21，部门领导',
            '需要补充风险评估内容-2025-08-22，部门领导',
            '修改后同意通过-2025-08-23，部门领导'
          ],
          leaderOpinions: [
            '总体方案可行-2025-08-21，总经理',
            '注意控制风险-2025-08-22，总经理',
            '同意执行-2025-08-23，总经理'
          ]
        },
        step3: {
          departmentOpinions: [
            '按计划执行清退工作-2025-08-24，部门领导'
          ],
          leaderOpinions: [
            '严格按照计划执行-2025-08-24，总经理'
          ]
        },
        step4: {
          work: '产权登记变更、工商变更登记',
          progress: '正在办理中'
        }
      }
      this.progressDialogVisible = true
    },

    // 查看计划文件
    handleViewPlan () {
      this.$message.info('查看年度参股企业整改计划文件')
      // 这里可以实现文件预览或下载功能
    },

    // 查看附件
    handleViewAttachment (type) {
      const typeMap = {
        property: '产权登记附件',
        business: '工商变更附件',
        contract: '合同附件'
      }
      this.$message.info(`查看${typeMap[type]}`)
      // 这里可以实现附件预览或下载功能
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.fetchData()
    }
  }
}
</script>


<style lang="scss" scoped>
.custom-table-container {
  padding: 16px;
  background-color: #f5f7fa;
}

.search-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}
</style>