<template>
  <span>
    <el-date-picker v-if="config.type !== 'daterange'"
      v-model="formModel[config.prop]"
      :type="config.type"
      :clearable="config.clearable"
      :disabled="config.disabled"
      :readonly="config.readonly"
      :format="config.format"
      :value-format="config.valueFormat"
      :placeholder="config.placeholder"
      :style="config.style"
      @blur="(e) => handleEvent(e, config.blur)"
      @focus="(e) => handleEvent(e, config.focus)"
      @change="(e) => handleEvent(e, config.change)">
    </el-date-picker>
    <el-date-picker v-else
      v-model="formModel[config.prop]"
      :type="config.type"
      :range-separator="config.rangeText"
      :start-placeholder="config.startPlaceHolder"
      :end-placeholder="config.endPlaceHolder"
      :clearable="config.clearable"
      :disabled="config.disabled"
      :readonly="config.readonly"
      :format="config.format"
      :value-format="config.valueFormat"
      :style="config.style"
      @blur="(e) => handleEvent(e, config.blur)"
      @focus="(e) => handleEvent(e, config.focus)"
      @change="(e) => handleEvent(e, config.change)">
    </el-date-picker>
  </span>
</template>

<script>
  export default {
    name: "DateTime",
    props: {
      config: Object,
      formModel: Object
    },
    methods: {
      handleEvent(value, formEvent) {
        if(formEvent) {
          return formEvent(value)
        }
      }
    },
  }
</script>