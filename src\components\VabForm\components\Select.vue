<template>
  <el-select 
    v-model="formModel[config.prop]" 
    :placeholder="config.placeholder"
    :disabled="config.disabled"
    :multiple="config.multiple"
    :clearable="config.clearable"
    :style="config.style"
    :filterable="config.filterable"
    @blur="(e) => handleEvent(e, config.blur)"
    @focus="(e) => handleEvent(e, config.focus)"
    @clear="(e) => handleEvent(e, config.clear)"
    @change="(e) => handleEvent(e, config.change)">
    <el-option
      v-for="option in config.options" 
      :key="config.props ? option[config.props.value] : option.value"
      :label="config.props ? option[config.props.label] : option.label"
      :value="config.props ? option[config.props.value] : option.value"
    />
  </el-select >
</template>

<script>
  export default {
    name: "Select",
    props: {
      config: Object,
      formModel: Object
    },
    methods: {
      handleEvent(value, formEvent) {
        if(formEvent) {
          return formEvent(value)
        }
      }
    },
  }
</script>