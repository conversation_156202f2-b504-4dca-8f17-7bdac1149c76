<!--修正弹窗-->
<template>
  <BaseDialog
    title="修正"
    :visible.sync="dialogVisible"
    type="edit"
    size="Small"
    :close-on-click-modal="false"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <el-form ref="correctForm" :model="formData" label-width="80px" class="correct-form">
      <el-form-item label="修正结果" prop="correctResult">
        <el-select
          v-model="formData.correctResult"
          placeholder="请选择修正结果"
          style="width: 100%"
        >
          <el-option label="关注" value="关注"></el-option>
          <el-option label="清退" value="清退"></el-option>
          <el-option label="保留" value="保留"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="是否保留" prop="isKeep">
        <el-select
          v-model="formData.isKeep"
          placeholder="请选择是否保留"
          style="width: 100%"
        >
          <el-option label="否" value="否"></el-option>
          <el-option label="是" value="是"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </BaseDialog>
</template>

<script>
import BaseDialog from '@/components/BaseDialog/index.vue'

export default {
  name: "correctDialog",
  components: {
    BaseDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 初始数据
    initialData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formData: {
        correctResult: '',
        isKeep: ''
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initFormData()
      }
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      this.formData = {
        correctResult: this.initialData.correctResult || '',
        isKeep: this.initialData.isKeep || ''
      }
    },

    // 确认提交
    handleConfirm() {
      this.$refs.correctForm.validate((valid) => {
        if (valid) {
          this.$emit('confirm', { ...this.formData })
          this.dialogVisible = false
        }
      })
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.correct-form {
  padding: 20px 0;
}

.correct-form .el-form-item {
  margin-bottom: 20px;
}
</style>
