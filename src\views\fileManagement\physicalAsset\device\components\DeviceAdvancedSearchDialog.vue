<!--设备资产高级查询弹窗组件-->
<template>
  <base-dialog title="高级查询" type="edit" :visible.sync="dialogVisible" size="Max" @confirm="handleConfirm" @cancel="handleCancel">
    <el-form ref="searchForm" :model="searchForm" label-width="120px" class="advanced-search-form">
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-title">基本信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="资产名称">
              <el-input v-model="searchForm.zcfaAssetsName" placeholder="请输入资产名称" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产权单位">
              <el-input v-model="searchForm.companyName" placeholder="请输入产权单位" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="资产状态">
              <el-select v-model="searchForm.zcfaAssetsState" placeholder="请选择资产状态" clearable>
                <el-option label="正常" value="正常" />
                <el-option label="闲置" value="闲置" />
                <el-option label="报废" value="报废" />
                <el-option label="维修" value="维修" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="资产类型">
              <el-input v-model="searchForm.zcfaType" placeholder="请输入资产类型" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="制造厂商">
              <el-input v-model="searchForm.zcfaManufactor" placeholder="请输入制造厂商" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="型号">
              <el-input v-model="searchForm.zcfaModel" placeholder="请输入型号" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="序号">
              <el-input v-model="searchForm.zcfaSerialNumber" placeholder="请输入序号" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="资产编号">
              <el-input v-model="searchForm.zcfaSerialNum" placeholder="请输入资产编号" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="存放地点">
              <el-input v-model="searchForm.zcfaStoragePlace" placeholder="请输入存放地点" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="分类">
              <el-input v-model="searchForm.zcfaCategory" placeholder="请输入分类" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="资产用途">
              <el-input v-model="searchForm.zcfaUse" placeholder="请输入资产用途" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="资金来源">
              <el-input v-model="searchForm.zcfaFundsSource" placeholder="请输入资金来源" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否固定资产">
              <el-input v-model="searchForm.zcfaIfAssets" placeholder="请输入是否固定资产" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 数值信息 -->
      <div class="form-section">
        <div class="section-title">数值信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="账面原值(万元)">
              <el-input v-model="searchForm.zcfaBookValue" placeholder="请输入账面原值" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账面净值(万元)">
              <el-input v-model="searchForm.zcfaNetbookValue" placeholder="请输入账面净值" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="折旧年限(年)">
              <el-input v-model="searchForm.zcfaUsefulLife" placeholder="请输入折旧年限" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 日期信息 -->
      <div class="form-section">
        <div class="section-title">日期信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="生产日期开始">
              <el-date-picker
                v-model="searchForm.zcfaProductDateStart"
                type="date"
                placeholder="请选择生产日期开始"
                value-format="yyyy-MM-dd"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="生产日期结束">
              <el-date-picker
                v-model="searchForm.zcfaProductDateEnd"
                type="date"
                placeholder="请选择生产日期结束"
                value-format="yyyy-MM-dd"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入账日期开始">
              <el-date-picker
                v-model="searchForm.zcfaRecordDateStart"
                type="date"
                placeholder="请选择入账日期开始"
                value-format="yyyy-MM-dd"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="入账日期结束">
              <el-date-picker
                v-model="searchForm.zcfaRecordDateEnd"
                type="date"
                placeholder="请选择入账日期结束"
                value-format="yyyy-MM-dd"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 联系信息 -->
      <div class="form-section">
        <div class="section-title">联系信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="联系人">
              <el-input v-model="searchForm.zcfaContacts" placeholder="请输入联系人" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话">
              <el-input v-model="searchForm.zcfaContactsPhone" placeholder="请输入联系电话" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 其他信息 -->
      <div class="form-section">
        <div class="section-title">其他信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="备注">
              <el-input v-model="searchForm.zcfaRemark" placeholder="请输入备注" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleReset">重置</el-button>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">查询</el-button>
      </div>
    </template>
  </base-dialog>
</template>

<script>
import BaseDialog from '@/components/BaseDialog/index.vue'

export default {
  name: 'DeviceAdvancedSearchDialog',
  components: {
    BaseDialog
  },
  props: {
    // 外部传入的查询条件
    externalSearchForm: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      dialogVisible: false,
      searchForm: {
        zcfaAssetsName: '',
        companyName: '',
        zcfaAssetsState: '',
        zcfaType: '',
        zcfaManufactor: '',
        zcfaModel: '',
        zcfaSerialNumber: '',
        zcfaSerialNum: '',
        zcfaStoragePlace: '',
        zcfaCategory: '',
        zcfaUse: '',
        zcfaFundsSource: '',
        zcfaIfAssets: '',
        zcfaBookValue: '',
        zcfaNetbookValue: '',
        zcfaUsefulLife: '',
        zcfaProductDateStart: '',
        zcfaProductDateEnd: '',
        zcfaRecordDateStart: '',
        zcfaRecordDateEnd: '',
        zcfaContacts: '',
        zcfaContactsPhone: '',
        zcfaRemark: ''
      }
    }
  },
  methods: {
    // 显示弹窗
    showDialog () {
      // 同步外部查询条件到弹窗表单
      this.syncExternalSearchForm()
      this.dialogVisible = true
    },

    // 同步外部查询条件
    syncExternalSearchForm () {
      // 将外部查询条件同步到弹窗表单中
      Object.keys(this.searchForm).forEach(key => {
        if (this.externalSearchForm[key] !== undefined) {
          this.searchForm[key] = this.externalSearchForm[key]
        }
      })
    },

    // 关闭弹窗
    closeDialog () {
      this.dialogVisible = false
    },

    // 确认查询
    handleConfirm () {
      // 创建完整的查询参数对象，包含所有字段（空值也保留，用于清空外部对应字段）
      const searchParams = { ...this.searchForm }

      // 清理字符串字段的前后空格
      Object.keys(searchParams).forEach(key => {
        if (typeof searchParams[key] === 'string') {
          searchParams[key] = searchParams[key].trim()
        }
      })

      this.$emit('search', searchParams)
      this.closeDialog()
    },

    // 取消
    handleCancel () {
      this.closeDialog()
    },

    // 重置表单
    handleReset () {
      this.$refs.searchForm.resetFields()
      // 手动重置所有字段
      Object.keys(this.searchForm).forEach(key => {
        this.searchForm[key] = ''
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.advanced-search-form {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 10px;
}

.form-section {
  margin-bottom: 30px;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #409eff;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      bottom: -2px;
      width: 30px;
      height: 2px;
      background-color: #409eff;
    }
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
}
</style>
