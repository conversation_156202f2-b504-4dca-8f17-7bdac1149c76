<template>
  <div class="custom-table-container">
    <div class="search-container">
      <div>
        <el-button type="primary" @click="handlePlanReview">审核计划</el-button>
      </div>
      <el-button type="primary">综合查询</el-button>
    </div>
    <div class="table-section">
      <el-table :data="tableData" :height="height" border stripe highlight-current-row style="width: 100%;" v-loading="loading" row-key="zchiId">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="zchiAssetsNo" label="资产编号" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiAssetsName" label="资产名称" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiUseDescribe" label="现状/用途" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="companyName" label="产权单位" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCertificateCode" label="产权证号" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiAddress" label="地理位置" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiHouseSource" label="取得方式" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDate" label="取得时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiArea" label="建筑面积(㎡)" width="120" align="center" />
        <el-table-column prop="zchiOriginalValue" label="原值(万元)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiNetValue" label="净值(万元)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiTotalDepreciation" label="累计折旧(万元)" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCountry" label="境内/境外" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIfAssets" label="是否两非资产" width="120" align="center" />
        <el-table-column prop="zchiIfExist" label="是否取得房屋产权证" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIfDispute" label="是否存在纠纷" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIfDispose" label="是否可处置" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIfMortgage" label="是否存在抵押" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiOperator" label="联系人" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiOperatorTel" label="联系电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiProvince" label="省份" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCity" label="城市" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCounty" label="区/县" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDeptName" label="业务管理部门" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDepartmentLeader" label="部门负责人" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDepartmentTel" label="部门电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCompanyLeader" label="分公司负责人" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCompanyTel" label="分公司电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiEvaluateValue" label="评估价值(万元)" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiEvaluateDate" label="评估日期" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiServiceLife" label="使用年限(年)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDepreciableYear" label="计提年限" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiOfficeArea" label="科研办公面积" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCommercialArea" label="商业面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiResidentialArea" label="住宅面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIndustrialArea" label="工业面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiUndergroundArea" label="地下建筑面积" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiOtherArea" label="其他面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiRemark" label="备注" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="createdTime" label="创建时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="createdBy" label="创建人" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="updatedTime" label="更新时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column label="操作" width="180" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleIsAccept(scope.row)">是否接受</el-button>
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
            <el-button type="text" size="small" @click="handleProgressReporting(scope.row)">进展填报</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination background class="el-pagination-a" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 审核计划弹窗 -->
    <PlanReviewDialog :visible.sync="planReviewDialogVisible" :initial-data="planReviewData" @confirm="handlePlanReviewConfirm" @view-plan="handleViewPlan" />

    <!-- 是否接受(清退)弹窗 -->
    <IsAcceptClear :visible.sync="isAcceptClearVisible" :initial-data="isAcceptClearData" @confirm="handleIsAcceptClearConfirm" />

    <!-- 是否接受(整改)弹窗 -->
    <IsAcceptCorrection :visible.sync="isAcceptCorrectionVisible" :initial-data="isAcceptCorrectionData" @confirm="handleIsAcceptCorrectionConfirm" />

    <!-- 进展填报弹窗 -->
    <ProgressReporting :visible.sync="progressReportingVisible" :initial-data="progressReportingData" @confirm="handleProgressReportingConfirm" />
  </div>
</template>

<script>
import { getBuildingsList } from '@/api/buildings'
import PlanReviewDialog from './components/planReviewDialog.vue'
import IsAcceptClear from './components/isAcceptClear.vue'
import IsAcceptCorrection from './components/isAcceptCorrection.vue'
import ProgressReporting from './components/progressReporting.vue'

export default {
  components: {
    PlanReviewDialog,
    IsAcceptClear,
    IsAcceptCorrection,
    ProgressReporting
  },
  data () {
    return {
      loading: false,
      height: this.$baseTableHeight(1, 1),
      searchForm: {
        pageNo: 1,
        pageSize: 20
      },
      tableData: [],
      total: 0,
      // 审核计划弹窗相关
      planReviewDialogVisible: false,
      planReviewData: {},
      // 是否接受(清退)弹窗相关
      isAcceptClearVisible: false,
      isAcceptClearData: {},
      // 是否接受(整改)弹窗相关
      isAcceptCorrectionVisible: false,
      isAcceptCorrectionData: {},
      // 进展填报弹窗相关
      progressReportingVisible: false,
      progressReportingData: {},
      currentRowData: null
    }
  },

  created () {
    this.fetchData()
  },
  methods: {
    fetchData () {
      this.loading = true
      getBuildingsList(this.searchForm).then(response => {
        if (response && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.tableData = []
          this.total = 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取数据失败')
      })
    },

    handleDetail (row) {
      console.log('查看详情:', row)
    },

    // 审核计划
    handlePlanReview () {
      this.planReviewData = {}
      this.planReviewDialogVisible = true
    },

    // 审核计划确认
    handlePlanReviewConfirm (formData) {
      console.log('审核计划提交:', formData)
      this.$message.success('审核计划提交成功')
    },

    // 查看计划
    handleViewPlan () {
      this.$message.info('查看计划文件')
      // 这里可以实现文件预览或下载功能
    },

    // 是否接受
    handleIsAccept (row) {
      this.currentRowData = row
      // 根据业务类型判断显示哪个弹窗
      // 这里假设根据某个字段判断是清退还是整改
      const businessType = row.businessType || 'clear' // 示例字段

      if (businessType === 'clear') {
        // 清退业务
        this.isAcceptClearData = {}
        this.isAcceptClearVisible = true
      } else {
        // 整改业务
        this.isAcceptCorrectionData = {}
        this.isAcceptCorrectionVisible = true
      }
    },

    // 是否接受(清退)确认
    handleIsAcceptClearConfirm (formData) {
      console.log('是否接受(清退)提交:', formData)
      this.$message.success('是否接受(清退)提交成功')
    },

    // 是否接受(整改)确认
    handleIsAcceptCorrectionConfirm (formData) {
      console.log('是否接受(整改)提交:', formData)
      this.$message.success('是否接受(整改)提交成功')
    },

    // 进展填报
    handleProgressReporting (row) {
      this.currentRowData = row
      this.progressReportingData = {}
      this.progressReportingVisible = true
    },

    // 进展填报确认
    handleProgressReportingConfirm (formData) {
      console.log('进展填报提交:', formData)
      this.$message.success('进展填报提交成功')
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.fetchData()
    }
  }
}
</script>


<style lang="scss" scoped>
.custom-table-container {
  padding: 16px;
  background-color: #f5f7fa;
}

.search-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}
</style>