<!--进展情况弹窗-->
<template>
  <BaseDialog
    title="进展情况"
    :visible.sync="dialogVisible"
    type="view"
    size="Middle"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="progress-content">
      <!-- 进展记录列表 -->
      <div class="progress-list">
        <div
          v-for="(record, index) in progressRecords"
          :key="index"
          class="progress-record"
        >
          <!-- 时间标题 -->
          <div class="record-date">{{ record.date }}</div>

          <!-- 记录内容 -->
          <div class="record-content">
            <div class="form-row">
              <div class="form-item">
                <label>是否完成</label>
                <el-select
                  v-model="record.isCompleted"
                  placeholder="请选择"
                  style="width: 200px"
                  :disabled="readonly"
                >
                  <el-option label="是" value="是"></el-option>
                  <el-option label="否" value="否"></el-option>
                  <el-option label="部分完成" value="部分完成"></el-option>
                </el-select>
              </div>
            </div>

            <div class="form-row">
              <div class="form-item full-width">
                <label>进展情况</label>
                <el-input
                  v-model="record.progress"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入进展情况"
                  maxlength="500"
                  show-word-limit
                  resize="none"
                  :readonly="readonly"
                />
              </div>
            </div>

            <div class="form-row">
              <div class="form-item">
                <label>查看证明材料</label>
                <div class="attachment-list">
                  <div
                    v-for="(file, fileIndex) in record.attachments"
                    :key="fileIndex"
                    class="attachment-item"
                    @click="handleViewAttachment(file)"
                  >
                    {{ file.name }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BaseDialog>
</template>

<script>
import BaseDialog from '@/components/BaseDialog/index.vue'

export default {
  name: "progressDialog",
  components: {
    BaseDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 是否只读模式
    readonly: {
      type: Boolean,
      default: true
    },
    // 进展记录数据
    progressRecords: {
      type: Array,
      default: () => [
        {
          date: '2025-07-11',
          isCompleted: '是',
          progress: '',
          attachments: [
            { name: 'xxx文件', url: '#' },
            { name: 'xxx文件', url: '#' },
            { name: 'xxx文件', url: '#' }
          ]
        },
        {
          date: '2025-08-11',
          isCompleted: '是',
          progress: '',
          attachments: [
            { name: 'xxx文件', url: '#' },
            { name: 'xxx文件', url: '#' },
            { name: 'xxx文件', url: '#' }
          ]
        }
      ]
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    // 查看附件
    handleViewAttachment(file) {
      this.$emit('view-attachment', file)
      this.$message.info(`查看文件：${file.name}`)
    },

    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.progress-content {
  padding: 10px 0;
}

.progress-list {
  max-height: 500px;
  overflow-y: auto;
}

.progress-record {
  margin-bottom: 25px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 20px;
}

.progress-record:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.record-date {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
  background-color: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
}

.record-content {
  padding-left: 15px;
}

.form-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  flex: 1;
}

.form-item.full-width {
  width: 100%;
}

.form-item label {
  min-width: 100px;
  color: #606266;
  font-weight: bold;
  padding-top: 8px;
  flex-shrink: 0;
}

.form-item .el-select,
.form-item .el-input {
  flex: 1;
}

/* 附件列表样式 */
.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
  flex: 1;
}

.attachment-item {
  color: #409eff;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
  font-size: 14px;
}

.attachment-item:hover {
  background-color: #ecf5ff;
  text-decoration: underline;
}

/* 文本域样式 */
.form-item .el-textarea__inner {
  font-family: inherit;
  line-height: 1.5;
}

/* 选择框样式 */
.form-item .el-select .el-input__inner {
  height: 36px;
  line-height: 36px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 8px;
  }

  .form-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .form-item label {
    min-width: auto;
    padding-top: 0;
  }

  .record-content {
    padding-left: 0;
  }
}
</style>
