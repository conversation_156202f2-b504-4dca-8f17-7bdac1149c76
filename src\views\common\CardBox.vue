<template>
  <div class="card-box-container">
    <div class="card-header" v-if="showHeader">
      <div class="titleLeft" style="display: flex;align-items:center">
        <!-- <img style="width: 26px;height: 16px" src="@/assets/common/cardTitleIcon.png" alt=""> -->
        {{ title }}
        <slot name="leftTitle"></slot>
      </div>
      <div class="titleRight">
        <slot name="rightTitle"></slot>
      </div>
    </div>
    <div class="card-body" :style="renderStyle">
      <slot></slot>
    </div>
    <div class="card-footer" v-if="isShowFooter">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script>
  export default {
    name: "CardBox",
    props: {
      title: {
        type: String,
      },
      height: {
        type: String,
      },
      showHeader: {
        type: Boolean,
        default: true,
      }
    },
    data() {
      return {
        isShowFooter: true,
      }
    },
    computed: {
      renderStyle() {
        return {
          height: this.height,
          'overflow-y': 'auto'
        }
      }
    },
    mounted() {
      if (this.$slots['footer']) {
        this.isShowFooter = true
      }else {
        this.isShowFooter = false
      }
    },
  }
</script>

<style lang="scss" scoped>
  .card-box-container {
    width: 100%;
    background: #FFFFFF;
    box-shadow: 0px 8px 20px 0px rgba(177,197,197,0.08);
    border-radius: 4px;
    border: 1px solid #E4E7ED;
    padding: 0px!important;
    .card-header {
      height: 57px;
      border-bottom: 1px solid #E4E7ED;
      display: flex;
      align-items: center;
      padding: 0 16px;
      font-size: 20px;
      font-weight: 600;
      color: #CC1214;
      display: flex;
      align-items: center;
      justify-content: space-between;
      img {
        margin-right: 8px;
      }
      ::v-deep .el-form-item {
        margin-bottom: 0px;
      }
    }
    .card-body {
      padding: 16px;
    }
    .card-footer {
      height: 56px;
      background: #FFFFFF;
      border-top: 1px solid #E4E7ED;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 0 16px;
    }
  }
</style>