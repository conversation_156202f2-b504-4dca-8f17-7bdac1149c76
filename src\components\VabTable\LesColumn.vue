<template>
  <el-table-column :prop="col.prop" :label-class-name="col.prop" :label="col.label" :align="col.align" :header-align="col.headerAlign"  :width="col.width" :show-overflow-tooltip="col.showOverflowTooltip" :sortMethodFun="sortMethodFun" :sortable="col.sortable">
    <template v-if="col.children" >
      <template v-for="(item, index) in col.children">
        <LesColumn v-if="item.children" :key="index" :col="item" />
        <el-table-column v-else :prop="item.prop" :label-class-name="item.prop" :label="item.label" :align="item.align" :header-align="item.headerAlign"  :width="col.width">
          <template slot-scope="scope">
            <LesRender v-if="col.render" :scope="scope" :render="col.render" />
            <template v-else>{{ scope.row[item.prop] }}</template>
          </template>
        </el-table-column>
        <template slot-scope="scope">
          <LesRender v-if="col.render" :scope="scope" :render="col.render" />
          <template v-else>{{ scope.row[item.prop] }}</template>
        </template>
      </template>
    </template>
  </el-table-column>
</template>

<script>
  import LesRender from './LesRender'
  export default {
    name: "LesColumn",
    components: {
      LesRender
    },
    props: {
      col: {
        type: Object,
        default: () => {
          return {}
        }
      },
      sortMethodFun:{
        type: Function,
        default:()=>{}
      }
    }
  }
</script>