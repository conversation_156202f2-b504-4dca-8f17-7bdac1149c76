import request from '@/utils/request'

//获取投委会专家库列表数据
export function getInsideMarketData(params){
  return request({
    url: '/tzgl-twh-expert/vPageList',
    method: 'get',
    params,
  })
}

// 新增或修改投委会专家库数据
export function saveInsideMarketData(data){
  return request({
    url: '/tzgl-twh-expert/saveOrUpdLog',
    method: 'post',
    data,
  })
}

// 删除投委会专家库数据
export function deleteInsideMarketData(data){
  return request({
    url: '/tzgl-twh-expert/deleteLog',
    method: 'post',
    data,
  })
}


//获取联系人列表数据
export function getLinkUserData(params){
  return request({
    url: '/tzgl-twh-expert-link/vPageList',
    method: 'get',
    params,
  })
}

// 新增或修改联系人数据
export function saveLinkUserData(data){
  return request({
    url: '/tzgl-twh-expert-link/saveOrUpdLog',
    method: 'post',
    data,
  })
}

// 删除联系人数据
export function deleteLinkUserData(data){
  return request({
    url: '/tzgl-twh-expert-link/deleteLog',
    method: 'post',
    data,
  })
}
