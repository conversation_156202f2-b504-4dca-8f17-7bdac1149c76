import request from '@/utils/request'

// 分页查询参股企业治理信息表
export function getCompanyGoverList(query) {
  return request({
    url: '/zc-cggq-company-governance/vPage',
    method: 'get',
    params: query
  })
}

// 新增参股企业治理信息
export function addCompanyGover(data) {
  return request({
    url: '/zc-cggq-company-governance/save',
    method: 'post',
    data: data
  })
}

// 修改参股企业治理信息
export function updateCompanyGover(data) {
  return request({
    url: '/zc-cggq-company-governance/update',
    method: 'post',
    data: data
  })
}

// 删除参股企业治理信息
export function deleteCompanyGover(ids) {
  return request({
    url: '/zc-cggq-company-governance/delete',
    method: 'post',
    data: { ids: ids }
  })
}

// 通过id查询参股企业治理信息
export function getCompanyGoverById(id) {
  return request({
    url: `/zc-cggq-company-governance/get-by-id/${id}`,
    method: 'get'
  })
}
