<template>
  <div class="custom-table-container">
    <div class="search-container">
      <div>
        <el-button type="primary" @click="handleIncludeNextYear">纳入下一年整改计划</el-button>
      </div>
      <el-button type="primary">综合查询</el-button>
    </div>
    <div class="table-section">
      <el-table :data="tableData" :height="height" border stripe highlight-current-row style="width: 100%;" v-loading="loading" row-key="zchiId">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="zchiAssetsNo" label="资产编号" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiAssetsName" label="资产名称" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiUseDescribe" label="现状/用途" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="companyName" label="产权单位" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCertificateCode" label="产权证号" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiAddress" label="地理位置" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiHouseSource" label="取得方式" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDate" label="取得时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiArea" label="建筑面积(㎡)" width="120" align="center" />
        <el-table-column prop="zchiOriginalValue" label="原值(万元)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiNetValue" label="净值(万元)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiTotalDepreciation" label="累计折旧(万元)" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCountry" label="境内/境外" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIfAssets" label="是否两非资产" width="120" align="center" />
        <el-table-column prop="zchiIfExist" label="是否取得房屋产权证" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIfDispute" label="是否存在纠纷" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIfDispose" label="是否可处置" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIfMortgage" label="是否存在抵押" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiOperator" label="联系人" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiOperatorTel" label="联系电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiProvince" label="省份" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCity" label="城市" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCounty" label="区/县" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDeptName" label="业务管理部门" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDepartmentLeader" label="部门负责人" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDepartmentTel" label="部门电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCompanyLeader" label="分公司负责人" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCompanyTel" label="分公司电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiEvaluateValue" label="评估价值(万元)" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiEvaluateDate" label="评估日期" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiServiceLife" label="使用年限(年)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDepreciableYear" label="计提年限" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiOfficeArea" label="科研办公面积" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCommercialArea" label="商业面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiResidentialArea" label="住宅面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIndustrialArea" label="工业面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiUndergroundArea" label="地下建筑面积" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiOtherArea" label="其他面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiRemark" label="备注" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="createdTime" label="创建时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="createdBy" label="创建人" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="updatedTime" label="更新时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column label="操作" width="120" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
            <el-button type="text" size="small" @click="handleViewProgress(scope.row)">查看进展</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination background class="el-pagination-a" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 进展情况弹窗 -->
    <ProgressDialog :visible.sync="progressDialogVisible" :readonly="true" :progress-records="currentProgressRecords" @view-attachment="handleViewAttachment" />
  </div>
</template>

<script>
import { getBuildingsList } from '@/api/buildings'
import ProgressDialog from './components/progressDialog.vue'

export default {
  components: {
    ProgressDialog
  },
  data () {
    return {
      loading: false,
      height: this.$baseTableHeight(1, 1),
      searchForm: {
        pageNo: 1,
        pageSize: 20
      },
      tableData: [],
      total: 0,
      // 进展情况弹窗相关
      progressDialogVisible: false,
      currentProgressRecords: [],
      currentRowData: null
    }
  },

  created () {
    this.fetchData()
  },
  methods: {
    fetchData () {
      this.loading = true
      getBuildingsList(this.searchForm).then(response => {
        if (response && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.tableData = []
          this.total = 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取数据失败')
      })
    },

    handleDetail (row) {
      console.log('查看详情:', row)
    },

    // 纳入下一年整改计划
    handleIncludeNextYear () {
      this.$message.info('纳入下一年整改计划功能')
      // 这里可以实现具体的业务逻辑
    },

    // 查看进展情况
    handleViewProgress (row) {
      this.currentRowData = row
      // 根据实际业务逻辑设置进展记录数据
      this.currentProgressRecords = [
        {
          date: '2025-07-11',
          isCompleted: '是',
          progress: '已完成初步整改方案制定，相关文件已提交审核。整改工作按计划推进中，预计下月完成全部整改任务。',
          attachments: [
            { name: '整改方案文件.pdf', url: '#' },
            { name: '进度报告.docx', url: '#' },
            { name: '相关证明材料.jpg', url: '#' }
          ]
        },
        {
          date: '2025-08-11',
          isCompleted: '部分完成',
          progress: '整改工作进展顺利，已完成70%的整改任务。剩余工作主要集中在设备更新和流程优化方面，预计本月底前全部完成。',
          attachments: [
            { name: '阶段性报告.pdf', url: '#' },
            { name: '设备更新清单.xlsx', url: '#' },
            { name: '现场照片.jpg', url: '#' }
          ]
        },
        {
          date: '2025-08-21',
          isCompleted: '否',
          progress: '本月计划完成剩余整改工作，目前正在进行最后阶段的设备调试和系统测试。',
          attachments: [
            { name: '测试报告.pdf', url: '#' },
            { name: '调试记录.docx', url: '#' }
          ]
        }
      ]
      this.progressDialogVisible = true
    },

    // 查看附件
    handleViewAttachment (file) {
      this.$message.info(`查看文件：${file.name}`)
      // 这里可以实现文件预览或下载功能
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.fetchData()
    }
  }
}
</script>


<style lang="scss" scoped>
.custom-table-container {
  padding: 16px;
  background-color: #f5f7fa;
}

.search-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}
</style>