<!--园区资产档案-->
<template>
  <div class="custom-table-container">
    <!-- 顶部统计卡片 -->
    <div class="statistics-cards">
      <div class="stat-card">
        <div class="icon-wrapper red-bg">
          <i class="el-icon-s-home" />
        </div>
        <div class="stat-info">
          <div class="stat-title">总园区数量</div>
          <div class="stat-value red-text">{{ statistics.totalCount || 0 }}<span>个</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper orange-bg">
          <i class="el-icon-money" />
        </div>
        <div class="stat-info">
          <div class="stat-title">园区总价值</div>
          <div class="stat-value orange-text">{{ statistics.totalValue || 0 }}<span>亿元</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper blue-bg">
          <i class="el-icon-data-line" />
        </div>
        <div class="stat-info">
          <div class="stat-title">本年度处置项目数</div>
          <div class="stat-value blue-text">{{ statistics.totalArea || 0 }}<span>项</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper purple-bg">
          <i class="el-icon-key" />
        </div>
        <div class="stat-info">
          <div class="stat-title">本年度租赁项目数</div>
          <div class="stat-value purple-text">{{ statistics.disposableCount || 0 }}<span>项</span></div>
        </div>
      </div>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" class="form-inline">
        <div class="leftRight">
          <div class="leftBar">
            <el-form-item label="资产名称:">
              <el-input v-model="searchForm.zpkAssetName" placeholder="请输入资产名称" class="inputW" />
            </el-form-item>
            <el-form-item label="资产编号:">
              <el-input v-model="searchForm.zpkAssetNumber" placeholder="请输入资产编号" class="inputW" />
            </el-form-item>
            <el-form-item label="所在地区:">
              <el-input v-model="searchForm.zpkLocationProvinceCityDistrict" placeholder="请输入所在地区" class="inputW" />
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" icon="el-icon-search" @click="handleAdvancedSearch">高级查询</el-button>
            <el-button @click="resetQuery" style="margin-right:10px">重置</el-button>
            <ExportButton :export-api="exportParks" table-selector="#parkTable" :query-form="searchForm" file-name="园区资产档案.xls" excel-title="园区资产档案" :date-fields="exportDateFields" :show-dropdown="true" :all-data-page-size="10000" button-type="primary" :auto-exclude-operations="true" :exclude-columns="[]" :table-columns="tableColumns" @export-success="handleExportSuccess" @export-error="handleExportError" @export-all-success="handleExportAllSuccess" @export-all-error="handleExportAllError" />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table id="parkTable" :data="tableData" :height="470" border stripe highlight-current-row style="width: 100%;" v-loading="loading" row-key="zpkId">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="zpkAssetNumber" label="资产编号" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkAssetName" label="资产名称" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkAssetType" label="资产类型" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkCurrentUsageDescription" label="现状用途" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkLocationProvinceCityDistrict" label="所在地区" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkSpecificLocation" label="具体位置" width="180" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkTotalArea" label="占地面积(㎡)" width="120" align="center" />
        <el-table-column prop="zpkOriginalValue" label="原值(万元)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkNetValue" label="净值(万元)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkMainBusinessDirection" label="主要经营方向" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkDomesticOrForeign" label="境内/境外" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkIsNonCoreAsset" label="是否两非资产" width="120" align="center" />
        <el-table-column prop="zpkHasDispute" label="是否存在纠纷" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkHasMortgage" label="是否存在抵押" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkOperator" label="联系人" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkOperatorContact" label="联系电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkResearchOfficeArea" label="科研办公面积" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkCommercialArea" label="商业面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkResidentialArea" label="住宅面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkIndustrialArea" label="工业面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkRentalArea" label="出租面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkSelfUseArea" label="自用面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkIdleArea" label="闲置面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkOtherArea" label="其他面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkMortgagedArea" label="已抵押面积" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkDepartmentLeader" label="部门负责人" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkDepartmentLeaderContact" label="部门电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkCompanyLeader" label="分公司负责人" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkCompanyLeaderContact" label="分公司电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkRemarks" label="备注" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkCreatedTime" label="创建时间" width="160" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkCreatedBy" label="创建人" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zpkUpdatedTime" label="更新时间" width="160" show-overflow-tooltip align="center" />
        <el-table-column label="操作" width="80" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination background class="el-pagination-a b-none" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 详情弹窗组件 -->
    <park-detail-dialog ref="parkDetail" />

    <!-- 高级查询弹窗组件 -->
    <park-advanced-search-dialog ref="parkAdvancedSearch" :external-search-form="searchForm" @search="handleAdvancedSearchSubmit" />
  </div>
</template>

<script>
import { getParksList, exportParks, getParksStats } from '@/api/park'
import ParkDetailDialog from './components/ParkDetailDialog.vue'
import ParkAdvancedSearchDialog from './components/ParkAdvancedSearchDialog.vue'
import ExportButton from '@/components/ExportButton/index.vue'

export default {
  name: "index",
  components: {
    ParkDetailDialog,
    ParkAdvancedSearchDialog,
    ExportButton
  },
  data () {
    return {
      searchForm: {
        zpkAssetName: '',
        zpkAssetNumber: '',
        zpkAssetType: '',
        zpkLocationProvinceCityDistrict: '',
        zpkMainBusinessDirection: '',
        zpkCurrentUsageDescription: '',
        zpkDomesticOrForeign: '',
        zpkHasDispute: '',
        // 高级查询字段
        zpkSpecificLocation: '',
        zpkTotalArea: '',
        zpkOriginalValue: '',
        zpkNetValue: '',
        zpkIsNonCoreAsset: '',
        zpkHasMortgage: '',
        zpkOperator: '',
        zpkOperatorContact: '',
        zpkResearchOfficeArea: '',
        zpkCommercialArea: '',
        zpkResidentialArea: '',
        zpkIndustrialArea: '',
        zpkRentalArea: '',
        zpkSelfUseArea: '',
        zpkIdleArea: '',
        zpkOtherArea: '',
        zpkMortgagedArea: '',
        zpkDepartmentLeader: '',
        zpkDepartmentLeaderContact: '',
        zpkCompanyLeader: '',
        zpkCompanyLeaderContact: '',
        zpkRemarks: '',
        zpkCreatedBy: '',
        pageNo: 1,
        pageSize: 10
      },
      tableData: [],
      total: 0,
      loading: false,
      statistics: {
        totalCount: 0,
        totalValue: 0,
        totalArea: 0,
        disposableCount: 0
      },

      // 表格列配置，用于导出
      tableColumns: [
        { prop: 'zpkAssetNumber', label: '资产编号' },
        { prop: 'zpkAssetName', label: '资产名称' },
        { prop: 'zpkAssetType', label: '资产类型' },
        { prop: 'zpkCurrentUsageDescription', label: '现状用途' },
        { prop: 'zpkLocationProvinceCityDistrict', label: '所在地区' },
        { prop: 'zpkSpecificLocation', label: '具体位置' },
        { prop: 'zpkTotalArea', label: '占地面积(㎡)' },
        { prop: 'zpkOriginalValue', label: '原值(万元)' },
        { prop: 'zpkNetValue', label: '净值(万元)' },
        { prop: 'zpkMainBusinessDirection', label: '主要经营方向' },
        { prop: 'zpkDomesticOrForeign', label: '境内/境外' },
        { prop: 'zpkIsNonCoreAsset', label: '是否两非资产' },
        { prop: 'zpkHasDispute', label: '是否存在纠纷' },
        { prop: 'zpkHasMortgage', label: '是否存在抵押' },
        { prop: 'zpkOperator', label: '联系人' },
        { prop: 'zpkOperatorContact', label: '联系电话' },
        { prop: 'zpkResearchOfficeArea', label: '科研办公面积' },
        { prop: 'zpkCommercialArea', label: '商业面积' },
        { prop: 'zpkResidentialArea', label: '住宅面积' },
        { prop: 'zpkIndustrialArea', label: '工业面积' },
        { prop: 'zpkRentalArea', label: '出租面积' },
        { prop: 'zpkSelfUseArea', label: '自用面积' },
        { prop: 'zpkIdleArea', label: '闲置面积' },
        { prop: 'zpkOtherArea', label: '其他面积' },
        { prop: 'zpkMortgagedArea', label: '已抵押面积' },
        { prop: 'zpkDepartmentLeader', label: '部门负责人' },
        { prop: 'zpkDepartmentLeaderContact', label: '部门电话' },
        { prop: 'zpkCompanyLeader', label: '分公司负责人' },
        { prop: 'zpkCompanyLeaderContact', label: '分公司电话' },
        { prop: 'zpkRemarks', label: '备注' },
        { prop: 'zpkCreatedTime', label: '创建时间' },
        { prop: 'zpkCreatedBy', label: '创建人' },
        { prop: 'zpkUpdatedTime', label: '更新时间' }
      ],

      // 导出日期字段配置
      exportDateFields: {
        zpkCreatedTime: { celltype: "text" },
        zpkUpdatedTime: { celltype: "text" }
      },

      // 导出API函数
      exportParks
    }
  },
  created () {
    this.fetchData()
    this.fetchStatistics()
  },
  methods: {
    fetchData () {
      this.loading = true
      const query = {
        ...this.searchForm
      }

      getParksList(query).then(response => {
        if (response && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.tableData = []
          this.total = 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取数据失败')
      })
    },

    fetchStatistics () {
      getParksStats().then(response => {
        if (response && response.data) {
          this.statistics = {
            totalCount: response.data.TOTAL_PARK_REGISTRATION || 0,
            totalValue: response.data.ROUND || 0,
            totalArea: response.data.TOTAL_AREA || 0,
            disposableCount: response.data.DISPOSABLE_COUNT || 0
          }
        }
      }).catch(() => {
        this.$message.error('获取统计数据失败')
      })
    },

    onSearch () {
      this.searchForm.pageNo = 1
      this.fetchData()
    },

    resetQuery () {
      // 重置所有查询字段
      Object.keys(this.searchForm).forEach(key => {
        this.searchForm[key] = ''
      })
      this.searchForm.pageNo = 1
      this.searchForm.pageSize = 10
      this.fetchData()
    },

    // 导出成功回调
    handleExportSuccess (response) {
      console.log('当前数据导出成功:', response)
      // 可以在这里添加额外的成功处理逻辑
    },

    // 导出失败回调
    handleExportError (error) {
      console.error('当前数据导出失败:', error)
      // 可以在这里添加额外的错误处理逻辑
    },

    // 全部数据导出成功回调
    handleExportAllSuccess (response) {
      console.log('全部数据导出成功:', response)
      // 可以在这里添加额外的成功处理逻辑
    },

    // 全部数据导出失败回调
    handleExportAllError (error) {
      console.error('全部数据导出失败:', error)
      // 可以在这里添加额外的错误处理逻辑
    },

    handleDetail (row) {
      this.$refs.parkDetail.showDialog(row)
    },

    handleAdvancedSearch () {
      this.$refs.parkAdvancedSearch.showDialog()
    },

    handleAdvancedSearchSubmit (searchParams) {
      // 直接使用高级查询的完整参数替换当前搜索表单
      this.searchForm = { ...searchParams }
      this.searchForm.pageNo = 1
      this.fetchData()
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = 1
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
.leftRight {
  display: flex;
  justify-content: space-between;
}
.inputW {
  width: 250px;
}

.custom-table-container {
  padding: 16px;
  background-color: #f5f7fa;
}

/* 顶部统计卡片样式 */
.statistics-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-right: 15px;
}

.stat-card:last-child {
  margin-right: 0;
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.icon-wrapper i {
  font-size: 24px;
  color: #fff;
}

.red-bg {
  background-color: rgba(245, 108, 108, 0.2);
}

.orange-bg {
  background-color: rgba(230, 162, 60, 0.2);
}

.blue-bg {
  background-color: rgba(64, 158, 255, 0.2);
}

.purple-bg {
  background-color: rgba(103, 194, 58, 0.2);
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
}

.stat-value span {
  font-size: 14px;
  margin-left: 4px;
}

.red-text {
  color: #f56c6c;
}

.orange-text {
  color: #e6a23c;
}

.blue-text {
  color: #409eff;
}

.purple-text {
  color: #67c23a;
}

/* 搜索表单样式 */
.search-form {
  background-color: #fff;
  padding: 20px 20px 0 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

/* 数据表格样式 */
.table-section {
  background: white;
  border-radius: 4px;
}
</style>