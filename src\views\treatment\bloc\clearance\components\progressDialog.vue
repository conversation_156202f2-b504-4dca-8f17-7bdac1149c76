<!--进展情况弹窗-->
<template>
  <BaseDialog
    title="进展情况"
    :visible.sync="dialogVisible"
    type="view"
    size="Middle"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="progress-content">
      <!-- 步骤进度条 -->
      <div class="progress-steps">
        <div
          v-for="(step, index) in steps"
          :key="index"
          :class="['step-item', {
            'active': currentStep === index + 1,
            'completed': currentStep > index + 1
          }]"
        >
          <div class="step-number">{{ index + 1 }}</div>
          <div class="step-content">
            <div class="step-title">{{ step.title }}</div>
            <div class="step-subtitle">{{ step.subtitle }}</div>
          </div>
        </div>
      </div>

      <!-- 步骤内容区域 -->
      <div class="step-content-area">
        <!-- 第一步：上报年度参股企业清退计划 -->
        <div v-if="currentStep === 1" class="step-detail">
          <div class="form-row">
            <div class="form-item">
              <label>办理人</label>
              <el-input v-model="mergedProgressData.step1.handler" readonly />
            </div>
            <div class="form-item">
              <label>办理部门</label>
              <el-input v-model="mergedProgressData.step1.department" readonly />
            </div>
          </div>
          <div class="form-row">
            <div class="form-item full-width">
              <label>年度参股企业整改计划</label>
              <el-button type="primary" @click="handleViewPlan">查看</el-button>
            </div>
          </div>
          <div class="info-text">{{ mergedProgressData.step1.info }}</div>
        </div>

        <!-- 第二步：审核年度参股企业清退计划 -->
        <div v-if="currentStep === 2" class="step-detail">
          <div class="approval-section">
            <div class="approval-item">
              <label>部门领导审核意见</label>
              <div class="approval-content">
                <div v-for="(opinion, index) in mergedProgressData.step2.departmentOpinions" :key="index" class="opinion-item">
                  {{ opinion }}
                </div>
              </div>
            </div>
            <div class="approval-item">
              <label>xx领导审核意见</label>
              <div class="approval-content">
                <div v-for="(opinion, index) in mergedProgressData.step2.leaderOpinions" :key="index" class="opinion-item">
                  {{ opinion }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 第三步：下达下级清退计划 -->
        <div v-if="currentStep === 3" class="step-detail">
          <div class="approval-section">
            <div class="approval-item">
              <label>部门领导下达意见</label>
              <div class="approval-content">
                <div v-for="(opinion, index) in mergedProgressData.step3.departmentOpinions" :key="index" class="opinion-item">
                  {{ opinion }}
                </div>
              </div>
            </div>
            <div class="approval-item">
              <label>xx领导下达意见</label>
              <div class="approval-content">
                <div v-for="(opinion, index) in mergedProgressData.step3.leaderOpinions" :key="index" class="opinion-item">
                  {{ opinion }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 第四步：实施实施清退任务 -->
        <div v-if="currentStep === 4" class="step-detail">
          <div class="implementation-section">
            <div class="form-row">
              <div class="form-item">
                <label>实施工作</label>
                <el-input v-model="mergedProgressData.step4.work" readonly />
              </div>
              <div class="form-item">
                <label>实施进度</label>
                <el-input v-model="mergedProgressData.step4.progress" readonly />
              </div>
            </div>
            <div class="form-row">
              <div class="form-item">
                <label>产权登记附件</label>
                <el-button type="primary" @click="handleViewAttachment('property')">查看</el-button>
              </div>
              <div class="form-item">
                <label>工商变更附件</label>
                <el-button type="primary" @click="handleViewAttachment('business')">查看</el-button>
              </div>
            </div>
            <div class="form-row">
              <div class="form-item">
                <label>合同附件</label>
                <el-button type="primary" @click="handleViewAttachment('contract')">查看</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BaseDialog>
</template>

<script>
import BaseDialog from '@/components/BaseDialog/index.vue'

export default {
  name: "ProgressDialog",
  components: {
    BaseDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 当前步骤
    currentStep: {
      type: Number,
      default: 1
    },
    // 进展数据
    progressData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      steps: [
        { title: '上报', subtitle: '年度参股企业清退计划' },
        { title: '审核', subtitle: '审核清退计划' },
        { title: '下达', subtitle: '下级清退计划' },
        { title: '实施', subtitle: '实施清退任务' }
      ],
      defaultProgressData: {
        step1: {
          handler: 'XXXX',
          department: 'XXXX',
          info: 'XXXX其他需要展示的内容等'
        },
        step2: {
          departmentOpinions: [
            'xxx需要修改-2025-08-21，xxx领导',
            'xxx需要修改-2025-08-22，xxx领导',
            'xxx同意-2025-08-23，xxx领导'
          ],
          leaderOpinions: [
            'xxx需要修改-2025-08-21，xxx领导',
            'xxx需要修改-2025-08-22，xxx领导',
            'xxx同意-2025-08-23，xxx领导'
          ]
        },
        step3: {
          departmentOpinions: [
            'xxxxxxx-2025-08-21，xxx领导',
            'xxxxxxx-2025-08-22，xxx领导',
            'xxxxxxx-2025-08-23，xxx领导'
          ],
          leaderOpinions: [
            'xxxxxxx-2025-08-21，xxx领导',
            'xxxxxxx-2025-08-22，xxx领导',
            'xxxxxxx-2025-08-23，xxx领导'
          ]
        },
        step4: {
          work: '批复/决议交易/产权登记/工商变更等',
          progress: '正在办理/完成等',
        }
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    // 合并默认数据和传入的数据
    mergedProgressData() {
      return {
        step1: { ...this.defaultProgressData.step1, ...(this.progressData.step1 || {}) },
        step2: { ...this.defaultProgressData.step2, ...(this.progressData.step2 || {}) },
        step3: { ...this.defaultProgressData.step3, ...(this.progressData.step3 || {}) },
        step4: { ...this.defaultProgressData.step4, ...(this.progressData.step4 || {}) }
      }
    }
  },
  methods: {
    // 查看计划
    handleViewPlan() {
      this.$emit('view-plan')
      this.$message.info('查看年度参股企业整改计划')
    },

    // 查看附件
    handleViewAttachment(type) {
      this.$emit('view-attachment', type)
      const typeMap = {
        property: '产权登记附件',
        business: '工商变更附件',
        contract: '合同附件'
      }
      this.$message.info(`查看${typeMap[type]}`)
    },

    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.progress-content {
  padding: 20px 0;
}

/* 步骤进度条样式 */
.progress-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(90deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  position: relative;
}

.progress-steps::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 10%;
  right: 10%;
  height: 2px;
  background-color: #e4e7ed;
  transform: translateY(-50%);
  z-index: 1;
}

.step-item {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
  flex: 1;
  max-width: 200px;
}

.step-item.active .step-number {
  background-color: #409eff;
  color: white;
}

.step-item.active .step-content {
  color: #409eff;
}

.step-item.completed .step-number {
  background-color: #67c23a;
  color: white;
}

.step-item.completed .step-content {
  color: #67c23a;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #e4e7ed;
  color: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  margin-right: 12px;
  flex-shrink: 0;
}

.step-content {
  color: #909399;
}

.step-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
}

.step-subtitle {
  font-size: 12px;
  color: #909399;
}

/* 步骤内容区域样式 */
.step-content-area {
  background-color: #fafafa;
  border-radius: 8px;
  padding: 20px;
  min-height: 300px;
}

.step-detail {
  width: 100%;
}

/* 表单行样式 */
.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-item.full-width {
  flex: none;
  width: 100%;
}

.form-item label {
  min-width: 120px;
  color: #606266;
  font-weight: bold;
  text-align: right;
}

.form-item .el-input {
  flex: 1;
}

.info-text {
  color: #409eff;
  font-size: 14px;
  margin-top: 15px;
  padding: 10px;
  background-color: #ecf5ff;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

/* 审批意见样式 */
.approval-section {
  display: flex;
  gap: 30px;
}

.approval-item {
  flex: 1;
}

.approval-item label {
  display: block;
  color: #606266;
  font-weight: bold;
  margin-bottom: 10px;
}

.approval-content {
  background-color: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  min-height: 120px;
}

.opinion-item {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 8px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.opinion-item:last-child {
  margin-bottom: 0;
}

/* 实施阶段样式 */
.implementation-section {
  width: 100%;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .progress-steps {
    flex-direction: column;
    gap: 15px;
  }

  .progress-steps::before {
    display: none;
  }

  .approval-section {
    flex-direction: column;
    gap: 20px;
  }

  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .form-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .form-item label {
    min-width: auto;
    text-align: left;
    margin-bottom: 5px;
  }
}
</style>
