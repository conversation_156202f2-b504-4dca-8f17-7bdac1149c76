<template>
  <el-form :ref="formRef" :model="formModel" :inline="formConfig.inline" :rules="rules" :label-width="formConfig.labelWidth + 'px'">
    <el-row :gutter="formConfig.gutter" v-if="!formConfig.inline">
      <template v-for="(config, index) in formItem">
        <el-col :span="config.span">
          <el-form-item :label="config.label" :prop="config.prop" :rules="config.rules">
            <Input v-if="config.name === 'input'" :config="config" :formModel="formModel"/>
            <InputNumber v-if="config.name === 'inputNumber'" :config="config" :formModel="formModel"/>
            <Radio v-if="config.name === 'radio'" :config="config" :formModel="formModel"/>
            <Checkbox v-if="config.name === 'checkbox'" :config="config" :formModel="formModel"/>
            <Select v-if="config.name === 'select'" :config="config" :formModel="formModel"/>
            <DateTime v-if="config.name === 'dateTime'" :config="config" :formModel="formModel"/>
            <Button v-if="config.name === 'button'" :config="config" :formModel="formModel"/>
            <SelectTree v-if="config.name === 'selectTree'" :config="config" :formModel="formModel"/>
          </el-form-item>
        </el-col>
      </template>
    </el-row>
    <template v-for="(config, index) in formItem" v-else>
      <el-form-item :label="config.name === 'button' ? '' : config.label" :prop="config.prop" :rules="config.rules">
        <Input v-if="config.name === 'input'" :config="config" :formModel="formModel"/>
        <InputNumber v-if="config.name === 'inputNumber'" :config="config" :formModel="formModel"/>
        <Radio v-if="config.name === 'radio'" :config="config" :formModel="formModel"/>
        <Checkbox v-if="config.name === 'checkbox'" :config="config" :formModel="formModel"/>
        <Select v-if="config.name === 'select'" :config="config" :formModel="formModel"/>
        <DateTime v-if="config.name === 'dateTime'" :config="config" :formModel="formModel"/>
        <Button v-if="config.name === 'button'" :config="config" :formModel="formModel"/>
        <SelectTree v-if="config.name === 'selectTree'" :config="config" :formModel="formModel"/>
      </el-form-item>
    </template>
  </el-form>
</template>

<script>
  import Input from './components/Input'
  import InputNumber from './components/InputNumber'
  import Radio from './components/Radio'
  import Checkbox from './components/Checkbox'
  import Select from './components/Select'
  import DateTime from './components/DateTime'
  import Button from './components/Button'
  import SelectTree from './components/SelectTree'
  export default {
    name: "VabForm",
    components: {
      Input,
      InputNumber,
      Radio,
      Checkbox,
      Select,
      DateTime,
      Button,
      SelectTree
    },
    props: {
      formRef: {
        type: String,
        default: () => {
          return 'form'
        }
      },
      rules: {
        type: Object,
      },
      formModel: {
        type: Object,
        default: () => {
          return {}
        }
      },
      formConfig: {
        type: Object,
        default: () => {
          return {
            gutter: 24,
            inline: false,
            labelWidth: 80,
          }
        }
      },
      formItem: {
        type: Array,
        default: () => {
          return []
        }
      },
    },
  }
</script>
