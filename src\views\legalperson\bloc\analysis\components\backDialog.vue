<!--选择回退计划弹窗-->
<template>
  <BaseDialog
    title="选择回退计划"
    :visible.sync="dialogVisible"
    type="edit"
    size="Max"
    :close-on-click-modal="false"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="back-dialog-content">
      <!-- 顶部筛选区域 -->
      <div class="filter-section">
        <el-row :gutter="10">
          <el-col :span="3.8">
            <el-select v-model="filterForm.planType" placeholder="二级流程单位" clearable>
              <el-option label="流程选项" value="流程选项"></el-option>
            </el-select>
          </el-col>
          <el-col :span="3.8">
            <el-select v-model="filterForm.enterprise" placeholder="企业名称" clearable>
              <el-option label="流程选项" value="流程选项"></el-option>
            </el-select>
          </el-col>
          <el-col :span="1">
            <el-button type="primary" @click="handleSearch">查询</el-button>
          </el-col>
          <el-col :span="1">
            <el-button @click="handleCancel">回退</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 表格区域 -->
      <div class="table-section">
        <el-table
          :data="tableData"
          border
          stripe
          height="400"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
        >
          <el-table-column type="selection" width="55" align="center"></el-table-column>
          <el-table-column type="index" label="序号" width="70" align="center"></el-table-column>

          <el-table-column prop="planName" label="二级流程单位" align="center" width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.planName }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="enterprise" label="企业名称" align="center" width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.enterprise }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="planCode" label="流程编号" align="center" width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.planCode }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="executionUnit" label="执行单位" align="center" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.executionUnit }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="planningUnit" label="计划单位" align="center" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.planningUnit }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="responsiblePerson" label="责任人" align="center" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.responsiblePerson }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="planYear" label="计划年度" align="center" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.planYear }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" align="center" width="80">
            <template slot-scope="scope">
              <div class="status-indicator">
                <span
                  class="status-dot"
                  :class="getStatusClass(scope.row.status)"
                ></span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="statusText" label="状态" align="center" width="80">
            <template slot-scope="scope">
              <span>{{ scope.row.statusText }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="remark" label="备注" align="center" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.remark }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="category" label="类别" align="center" width="80">
            <template slot-scope="scope">
              <span>{{ scope.row.category }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="priority" label="优先级" align="center" width="80">
            <template slot-scope="scope">
              <span>{{ scope.row.priority }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" width="180" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="handleSuggestion(scope.row)">建议</el-button>
              <el-button type="text" size="small" @click="handleCorrection(scope.row)">修正</el-button>
              <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          background
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

    </div>
  </BaseDialog>
</template>

<script>
import BaseDialog from '@/components/BaseDialog/index.vue'

export default {
  name: "backDialog",
  components: {
    BaseDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      filterForm: {
        planType: '',
        enterprise: ''
      },
      selectedRows: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      tableData: [
        {
          id: 1,
          planName: '生产计划A',
          enterprise: '中电科技',
          planCode: 'PLAN001',
          executionUnit: '是',
          planningUnit: '是',
          responsiblePerson: '张三',
          planYear: '2025',
          status: 'urgent',
          statusText: '清洁',
          remark: '紧急处理',
          category: '是',
          priority: '是'
        },
        {
          id: 2,
          planName: '维护计划B',
          enterprise: '电子集团',
          planCode: 'PLAN002',
          executionUnit: '是',
          planningUnit: '是',
          responsiblePerson: '李四',
          planYear: '2025',
          status: 'safe',
          statusText: '安全',
          remark: '定期维护',
          category: '否',
          priority: '是'
        },
        {
          id: 3,
          planName: '检修计划C',
          enterprise: '科技公司',
          planCode: 'PLAN003',
          executionUnit: '是',
          planningUnit: '是',
          responsiblePerson: '王五',
          planYear: '2025',
          status: 'normal',
          statusText: '保密',
          remark: '年度检修',
          category: '是',
          priority: '否'
        },
        {
          id: 4,
          planName: '升级计划D',
          enterprise: '信息技术',
          planCode: 'PLAN004',
          executionUnit: '否',
          planningUnit: '是',
          responsiblePerson: '赵六',
          planYear: '2025',
          status: 'urgent',
          statusText: '清洁',
          remark: '系统升级',
          category: '是',
          priority: '是'
        },
        {
          id: 5,
          planName: '培训计划E',
          enterprise: '教育部门',
          planCode: 'PLAN005',
          executionUnit: '是',
          planningUnit: '否',
          responsiblePerson: '孙七',
          planYear: '2025',
          status: 'safe',
          statusText: '安全',
          remark: '员工培训',
          category: '否',
          priority: '否'
        },
        {
          id: 6,
          planName: '研发计划F',
          enterprise: '研发中心',
          planCode: 'PLAN006',
          executionUnit: '是',
          planningUnit: '是',
          responsiblePerson: '周八',
          planYear: '2025',
          status: 'normal',
          statusText: '保密',
          remark: '新产品研发',
          category: '是',
          priority: '是'
        },
        {
          id: 7,
          planName: '质检计划G',
          enterprise: '质量部门',
          planCode: 'PLAN007',
          executionUnit: '是',
          planningUnit: '是',
          responsiblePerson: '吴九',
          planYear: '2025',
          status: 'urgent',
          statusText: '清洁',
          remark: '质量检测',
          category: '否',
          priority: '是'
        },
        {
          id: 8,
          planName: '销售计划H',
          enterprise: '销售部门',
          planCode: 'PLAN008',
          executionUnit: '否',
          planningUnit: '是',
          responsiblePerson: '郑十',
          planYear: '2025',
          status: 'safe',
          statusText: '安全',
          remark: '市场推广',
          category: '是',
          priority: '否'
        },
        {
          id: 9,
          planName: '采购计划I',
          enterprise: '采购部门',
          planCode: 'PLAN009',
          executionUnit: '是',
          planningUnit: '否',
          responsiblePerson: '钱十一',
          planYear: '2025',
          status: 'normal',
          statusText: '保密',
          remark: '设备采购',
          category: '是',
          priority: '是'
        },
        {
          id: 10,
          planName: '物流计划J',
          enterprise: '物流部门',
          planCode: 'PLAN010',
          executionUnit: '是',
          planningUnit: '是',
          responsiblePerson: '孙十二',
          planYear: '2025',
          status: 'safe',
          statusText: '安全',
          remark: '运输配送',
          category: '否',
          priority: '否'
        }
      ]
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.loadData()
      }
    }
  },
  methods: {
    // 显示弹窗
    showDialog() {
      this.dialogVisible = true
    },

    // 重置表单
    resetForm() {
      this.filterForm = {
        planType: '',
        enterprise: ''
      }
      this.selectedRows = []
    },

    // 获取状态样式类
    getStatusClass(status) {
      const statusMap = {
        'urgent': 'status-urgent',    // 红色
        'safe': 'status-safe',        // 黄色
        'normal': 'status-normal'     // 绿色
      }
      return statusMap[status] || 'status-normal'
    },

    // 加载数据
    loadData() {
      // 这里可以调用API获取数据
      this.pagination.total = this.tableData.length
      console.log('加载数据，总数:', this.pagination.total)
    },

    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadData()
    },

    // 重置
    handleReset() {
      this.resetForm()
      this.loadData()
    },

    // 选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    // 行点击
    handleRowClick(row) {
      console.log('点击行:', row)
    },

    // 建议
    handleSuggestion(row) {
      console.log('建议:', row)
      this.$emit('suggestion', row)
    },

    // 修正
    handleCorrection(row) {
      console.log('修正:', row)
      this.$emit('correction', row)
    },

    // 详情
    handleDetail(row) {
      console.log('详情:', row)
      this.$emit('detail', row)
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.loadData()
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.loadData()
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false
      this.resetForm()
    },

    // 确定
    handleConfirm() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择至少一条记录')
        return
      }
      this.$emit('confirm', this.selectedRows)
      this.dialogVisible = false
      this.resetForm()
    }
  }
}
</script>

<style scoped>
.back-dialog-content {
  .filter-section {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .table-section {
    margin-bottom: 20px;
  }

  .pagination-section {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }

  .status-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
  }

  .status-urgent {
    background-color: #f56c6c; /* 红色 */
  }

  .status-safe {
    background-color: #e6a23c; /* 黄色 */
  }

  .status-normal {
    background-color: #67c23a; /* 绿色 */
  }
}


/* 表格样式优化 */
::v-deep .el-table {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: #fafafa;
        color: #606266;
        font-weight: 600;
      }
    }
  }

  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

::v-deep .el-button--text {
  color: #409eff;

  &:hover {
    color: #66b1ff;
  }
}
</style>
