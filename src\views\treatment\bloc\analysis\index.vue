<template>
  <div class="custom-table-container">
    <div class="search-container">
      <div>
        <el-button type="primary">参股企业分析</el-button>
        <el-button type="primary">推送成员单位</el-button>
        <el-button type="primary" @click="handlePlanReview">计划审核</el-button>
      </div>
      <el-button type="primary">综合查询</el-button>
    </div>
    <div class="table-section">
      <el-table :data="tableData" :height="height" border stripe highlight-current-row style="width: 100%;" v-loading="loading" row-key="zchiId">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="zchiAssetsNo" label="资产编号" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiAssetsName" label="资产名称" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiUseDescribe" label="现状/用途" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="companyName" label="产权单位" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCertificateCode" label="产权证号" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiAddress" label="地理位置" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiHouseSource" label="取得方式" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDate" label="取得时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiArea" label="建筑面积(㎡)" width="120" align="center" />
        <el-table-column prop="zchiOriginalValue" label="原值(万元)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiNetValue" label="净值(万元)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiTotalDepreciation" label="累计折旧(万元)" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCountry" label="境内/境外" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIfAssets" label="是否两非资产" width="120" align="center" />
        <el-table-column prop="zchiIfExist" label="是否取得房屋产权证" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIfDispute" label="是否存在纠纷" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIfDispose" label="是否可处置" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIfMortgage" label="是否存在抵押" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiOperator" label="联系人" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiOperatorTel" label="联系电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiProvince" label="省份" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCity" label="城市" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCounty" label="区/县" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDeptName" label="业务管理部门" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDepartmentLeader" label="部门负责人" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDepartmentTel" label="部门电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCompanyLeader" label="分公司负责人" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCompanyTel" label="分公司电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiEvaluateValue" label="评估价值(万元)" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiEvaluateDate" label="评估日期" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiServiceLife" label="使用年限(年)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDepreciableYear" label="计提年限" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiOfficeArea" label="科研办公面积" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCommercialArea" label="商业面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiResidentialArea" label="住宅面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIndustrialArea" label="工业面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiUndergroundArea" label="地下建筑面积" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiOtherArea" label="其他面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiRemark" label="备注" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="createdTime" label="创建时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="createdBy" label="创建人" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="updatedTime" label="更新时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column label="操作" min-width="120" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleSuggestion(scope.row)">建议</el-button>
            <el-button type="text" size="small" @click="handleCorrection(scope.row)">修正</el-button>
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination background class="el-pagination-a" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 计划审核弹窗 -->
    <PlanReviewDialog :visible.sync="planReviewDialogVisible" :initial-data="planReviewData" @confirm="handlePlanReviewConfirm" @view-plan="handleViewPlan" @select-plan="handleSelectPlan" @suggestion-confirm="handlePlanSuggestionConfirm" @correction-confirm="handlePlanCorrectionConfirm" @detail="handlePlanDetail" />

    <!-- 建议弹窗 -->
    <SuggestionDialog :visible.sync="suggestionDialogVisible" :initial-data="suggestionData" @confirm="handleSuggestionConfirm" />

    <!-- 修正弹窗 -->
    <CorrectDialog :visible.sync="correctionDialogVisible" :initial-data="correctionData" @confirm="handleCorrectionConfirm" />
  </div>
</template>

<script>
import { getBuildingsList } from '@/api/buildings'
import PlanReviewDialog from './components/planReviewDialog.vue'
import SuggestionDialog from './components/suggestionDialog.vue'
import CorrectDialog from './components/correctDialog.vue'

export default {
  components: {
    PlanReviewDialog,
    SuggestionDialog,
    CorrectDialog
  },
  data () {
    return {
      loading: false,
      height: this.$baseTableHeight(1, 1),
      searchForm: {
        pageNo: 1,
        pageSize: 20
      },
      tableData: [],
      total: 0,
      // 计划审核弹窗相关
      planReviewDialogVisible: false,
      planReviewData: {},
      // 建议弹窗相关
      suggestionDialogVisible: false,
      suggestionData: {},
      // 修正弹窗相关
      correctionDialogVisible: false,
      correctionData: {}
    }
  },

  created () {
    this.fetchData()
  },
  methods: {
    fetchData () {
      this.loading = true
      getBuildingsList(this.searchForm).then(response => {
        if (response && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.tableData = []
          this.total = 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取数据失败')
      })
    },

    handleDetail (row) {
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.fetchData()
    },

    // 计划审核相关方法
    handlePlanReview () {
      this.planReviewData = {}
      this.planReviewDialogVisible = true
    },

    // 处理计划审核确认
    handlePlanReviewConfirm (formData) {
      console.log('计划审核数据:', formData)
      this.$message.success('计划审核提交成功')
      // 这里可以调用API提交审核数据
      // this.submitPlanReview(formData)
    },

    // 查看计划
    handleViewPlan () {
      this.$message.info('查看计划功能待实现')
      // 这里可以跳转到计划详情页面或打开计划查看弹窗
    },

    // 选择要回退的计划
    handleSelectPlan (selectedPlans) {
      console.log('选择的回退计划:', selectedPlans)
      this.$message.success(`已选择 ${selectedPlans.length} 个回退计划`)
      // 这里可以处理选择的计划数据，比如保存到某个变量或发送到后端
    },

    // 处理计划建议确认
    handlePlanSuggestionConfirm (data) {
      console.log('计划建议确认:', data)
      this.$message.success(`对计划 ${data.planData.planName} 的建议已提交`)
      // 这里可以调用API提交建议数据
    },

    // 处理计划修正确认
    handlePlanCorrectionConfirm (data) {
      console.log('计划修正确认:', data)
      this.$message.success(`对计划 ${data.planData.planName} 的修正已提交`)
      // 这里可以调用API提交修正数据
    },

    // 处理计划详情
    handlePlanDetail (row) {
      console.log('计划详情:', row)
      this.$message.info(`查看计划 ${row.planName} 的详情`)
      // 这里可以打开详情弹窗或跳转到详情页面
    },

    // 建议功能
    handleSuggestion (row) {
      console.log('建议操作，行数据:', row)
      this.suggestionData = { ...row }
      this.suggestionDialogVisible = true
    },

    // 修正功能
    handleCorrection (row) {
      console.log('修正操作，行数据:', row)
      this.correctionData = { ...row }
      this.correctionDialogVisible = true
    },

    // 处理建议确认
    handleSuggestionConfirm (formData) {
      console.log('建议数据:', formData)
      console.log('当前资产数据:', this.suggestionData)
      this.$message.success('建议提交成功')
      // 这里可以调用API提交建议数据
      // this.submitSuggestion(formData, this.suggestionData)
    },

    // 处理修正确认
    handleCorrectionConfirm (formData) {
      console.log('修正数据:', formData)
      console.log('当前资产数据:', this.correctionData)
      this.$message.success('修正提交成功')
      // 这里可以调用API提交修正数据
      // this.submitCorrection(formData, this.correctionData)
    }
  }
}
</script>


<style lang="scss" scoped>
.custom-table-container {
  padding: 16px;
  background-color: #f5f7fa;
}

.search-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}
</style>
