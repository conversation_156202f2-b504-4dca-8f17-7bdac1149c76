<template>
  <div class="custom-table-container">
    <div class="search-container">
      <div>
        <el-button type="primary" @click="handlePlanAnalysis">参股企业分析</el-button>
        <el-button type="primary">推送成员单位</el-button>
        <el-button type="primary" @click="handlePlanReview">计划审核</el-button>
      </div>
      <el-button type="primary" icon="el-icon-search">综合查询</el-button>
    </div>
    <div class="table-section">
      <el-table :data="tableData" :height="height" border stripe highlight-current-row style="width: 100%;" v-loading="loading" row-key="id">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="companyName" label="一级单位" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="subsidiaryName" label="参股企业名称" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="registeredCapital" label="注册资本(万元)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="shareholdingRatio" label="我方持股比例(%)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="investmentAmount" label="我方出资金额(万元)" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="acquisitionMethod" label="取得方式" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="acquisitionTime" label="取得时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="businessScope" label="主要经营范围" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="operatingStatus" label="经营状况" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="totalAssets" label="总资产" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="netAssets" label="净资产" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="revenue" label="营业收入" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="netProfit" label="净利润" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="domesticOverseas" label="境内/境外" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="boardMembers" label="董事会成员数" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="ourDirectors" label="我方董事数" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="supervisoryMembers" label="监事会成员数" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="ourSupervisors" label="我方监事数" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="seniorManagement" label="高级管理人员数" width="130" show-overflow-tooltip align="center" />
        <el-table-column prop="ourManagement" label="我方高管数" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="analysisStatus" label="分析状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.analysisStatus === '2025'" type="danger" size="small">2025</el-tag>
            <el-tag v-else-if="scope.row.analysisStatus === '未分析'" type="warning" size="small">未分析</el-tag>
            <el-tag v-else-if="scope.row.analysisStatus === '保留'" type="success" size="small">保留</el-tag>
            <el-tag v-else-if="scope.row.analysisStatus === '处置'" type="info" size="small">处置</el-tag>
            <span v-else>{{ scope.row.analysisStatus }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="contactPerson" label="联系人" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="contactPhone" label="联系电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="province" label="省份" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="city" label="城市" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="district" label="区/县" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="businessDepartment" label="业务管理部门" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="departmentLeader" label="部门负责人" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="departmentPhone" label="部门电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="companyLeader" label="分公司负责人" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="companyPhone" label="分公司电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="remarks" label="备注" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="createdTime" label="创建时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="createdBy" label="创建人" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="updatedTime" label="更新时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column label="操作" min-width="120" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleSuggestion(scope.row)">建议</el-button>
            <el-button type="text" size="small" @click="handleCorrection(scope.row)">修正</el-button>
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination background class="el-pagination-a" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 计划审核弹窗 -->
    <PlanReviewDialog :visible.sync="planReviewDialogVisible" :initial-data="planReviewData" @confirm="handlePlanReviewConfirm" @view-plan="handleViewPlan" @select-plan="handleSelectPlan" @suggestion-confirm="handlePlanSuggestionConfirm" @correction-confirm="handlePlanCorrectionConfirm" @detail="handlePlanDetail" />

    <!-- 建议弹窗 -->
    <SuggestionDialog :visible.sync="suggestionDialogVisible" :initial-data="suggestionData" @confirm="handleSuggestionConfirm" />

    <!-- 修正弹窗 -->
    <CorrectDialog :visible.sync="correctionDialogVisible" :initial-data="correctionData" @confirm="handleCorrectionConfirm" />
  </div>
</template>

<script>
import { getCompanyGoverList } from '@/api/treatment/analysis.js'
import PlanReviewDialog from './components/planReviewDialog.vue'
import SuggestionDialog from './components/suggestionDialog.vue'
import CorrectDialog from './components/correctDialog.vue'

export default {
  components: {
    PlanReviewDialog,
    SuggestionDialog,
    CorrectDialog
  },
  data () {
    return {
      loading: false,
      height: this.$baseTableHeight(1, 1),
      searchForm: {
        pageNo: 1,
        pageSize: 20
      },
      tableData: [],
      total: 0,
      // 计划审核弹窗相关
      planReviewDialogVisible: false,
      planReviewData: {},
      // 建议弹窗相关
      suggestionDialogVisible: false,
      suggestionData: {},
      // 修正弹窗相关
      correctionDialogVisible: false,
      correctionData: {}
    }
  },

  created () {
    this.fetchData()
  },
  methods: {
    fetchData () {
      this.loading = true
      getCompanyGoverList(this.searchForm).then(response => {
        if (response && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          // 如果接口暂时没有数据，使用模拟数据展示表格效果
          this.tableData = this.getMockData()
          this.total = this.tableData.length
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        // 接口调用失败时使用模拟数据
        this.tableData = this.getMockData()
        this.total = this.tableData.length
        console.warn('接口调用失败，使用模拟数据')
      })
    },

    // 模拟数据方法
    getMockData () {
      return [
        {
          id: '1',
          companyName: 'xxx',
          subsidiaryName: 'xxx',
          registeredCapital: 'xxx',
          shareholdingRatio: '否',
          investmentAmount: '否',
          acquisitionMethod: '否',
          acquisitionTime: '否',
          businessScope: '否',
          operatingStatus: '否',
          totalAssets: '否',
          netAssets: '2025',
          revenue: '●',
          netProfit: '清退',
          domesticOverseas: 'xxx',
          boardMembers: '否',
          ourDirectors: '否',
          supervisoryMembers: '建议',
          ourSupervisors: '处置',
          seniorManagement: '详情',
          ourManagement: '',
          analysisStatus: '2025',
          contactPerson: 'xxx',
          contactPhone: 'xxx',
          province: 'xxx',
          city: 'xxx',
          district: 'xxx',
          businessDepartment: 'xxx',
          departmentLeader: 'xxx',
          departmentPhone: 'xxx',
          companyLeader: 'xxx',
          companyPhone: 'xxx',
          remarks: 'xxx',
          createdTime: '2025-01-01',
          createdBy: 'admin',
          updatedTime: '2025-01-01'
        },
        {
          id: '2',
          companyName: '',
          subsidiaryName: '',
          registeredCapital: '',
          shareholdingRatio: '',
          investmentAmount: '',
          acquisitionMethod: '',
          acquisitionTime: '',
          businessScope: '',
          operatingStatus: '',
          totalAssets: '',
          netAssets: '2025',
          revenue: '●',
          netProfit: '关注',
          domesticOverseas: 'xxx',
          boardMembers: '有',
          ourDirectors: '否',
          supervisoryMembers: '建议',
          ourSupervisors: '评估',
          seniorManagement: '详情',
          ourManagement: '',
          analysisStatus: '未分析',
          contactPerson: '',
          contactPhone: '',
          province: '',
          city: '',
          district: '',
          businessDepartment: '',
          departmentLeader: '',
          departmentPhone: '',
          companyLeader: '',
          companyPhone: '',
          remarks: '',
          createdTime: '',
          createdBy: '',
          updatedTime: ''
        },
        {
          id: '3',
          companyName: '',
          subsidiaryName: '',
          registeredCapital: '',
          shareholdingRatio: '',
          investmentAmount: '',
          acquisitionMethod: '',
          acquisitionTime: '',
          businessScope: '',
          operatingStatus: '',
          totalAssets: '',
          netAssets: '',
          revenue: '●',
          netProfit: '保留',
          domesticOverseas: '',
          boardMembers: '无',
          ourDirectors: '否',
          supervisoryMembers: '建议',
          ourSupervisors: '评估',
          seniorManagement: '详情',
          ourManagement: '',
          analysisStatus: '保留',
          contactPerson: '',
          contactPhone: '',
          province: '',
          city: '',
          district: '',
          businessDepartment: '',
          departmentLeader: '',
          departmentPhone: '',
          companyLeader: '',
          companyPhone: '',
          remarks: '',
          createdTime: '',
          createdBy: '',
          updatedTime: ''
        },
        {
          id: '4',
          companyName: '',
          subsidiaryName: '',
          registeredCapital: '',
          shareholdingRatio: '',
          investmentAmount: '',
          acquisitionMethod: '',
          acquisitionTime: '',
          businessScope: '',
          operatingStatus: '',
          totalAssets: '',
          netAssets: '',
          revenue: '●',
          netProfit: '处置',
          domesticOverseas: 'xxx',
          boardMembers: '否',
          ourDirectors: '否',
          supervisoryMembers: '建议',
          ourSupervisors: '评估',
          seniorManagement: '详情',
          ourManagement: '',
          analysisStatus: '处置',
          contactPerson: '',
          contactPhone: '',
          province: '',
          city: '',
          district: '',
          businessDepartment: '',
          departmentLeader: '',
          departmentPhone: '',
          companyLeader: '',
          companyPhone: '',
          remarks: '',
          createdTime: '',
          createdBy: '',
          updatedTime: ''
        }
      ]
    },

    handleDetail (row) {
      console.log('查看详情:', row)
      this.$message.info('详情功能待实现')
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.fetchData()
    },

    handlePlanAnalysis () {
      this.fetchData()
    },

    // 计划审核相关方法
    handlePlanReview () {
      this.planReviewData = {}
      this.planReviewDialogVisible = true
    },

    // 处理计划审核确认
    handlePlanReviewConfirm (formData) {
      console.log('计划审核数据:', formData)
      this.$message.success('计划审核提交成功')
      // 这里可以调用API提交审核数据
      // this.submitPlanReview(formData)
    },

    // 查看计划
    handleViewPlan () {
      this.$message.info('查看计划功能待实现')
      // 这里可以跳转到计划详情页面或打开计划查看弹窗
    },

    // 选择要回退的计划
    handleSelectPlan (selectedPlans) {
      console.log('选择的回退计划:', selectedPlans)
      this.$message.success(`已选择 ${selectedPlans.length} 个回退计划`)
      // 这里可以处理选择的计划数据，比如保存到某个变量或发送到后端
    },

    // 处理计划建议确认
    handlePlanSuggestionConfirm (data) {
      console.log('计划建议确认:', data)
      this.$message.success(`对计划 ${data.planData.planName} 的建议已提交`)
      // 这里可以调用API提交建议数据
    },

    // 处理计划修正确认
    handlePlanCorrectionConfirm (data) {
      console.log('计划修正确认:', data)
      this.$message.success(`对计划 ${data.planData.planName} 的修正已提交`)
      // 这里可以调用API提交修正数据
    },

    // 处理计划详情
    handlePlanDetail (row) {
      console.log('计划详情:', row)
      this.$message.info(`查看计划 ${row.planName} 的详情`)
      // 这里可以打开详情弹窗或跳转到详情页面
    },

    // 建议功能
    handleSuggestion (row) {
      console.log('建议操作，行数据:', row)
      this.suggestionData = { ...row }
      this.suggestionDialogVisible = true
    },

    // 修正功能
    handleCorrection (row) {
      console.log('修正操作，行数据:', row)
      this.correctionData = { ...row }
      this.correctionDialogVisible = true
    },

    // 处理建议确认
    handleSuggestionConfirm (formData) {
      console.log('建议数据:', formData)
      console.log('当前资产数据:', this.suggestionData)
      this.$message.success('建议提交成功')
      // 这里可以调用API提交建议数据
      // this.submitSuggestion(formData, this.suggestionData)
    },

    // 处理修正确认
    handleCorrectionConfirm (formData) {
      console.log('修正数据:', formData)
      console.log('当前资产数据:', this.correctionData)
      this.$message.success('修正提交成功')
      // 这里可以调用API提交修正数据
      // this.submitCorrection(formData, this.correctionData)
    }
  }
}
</script>


<style lang="scss" scoped>
.custom-table-container {
  padding: 16px;
  background-color: #f5f7fa;
}

.search-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}
</style>
