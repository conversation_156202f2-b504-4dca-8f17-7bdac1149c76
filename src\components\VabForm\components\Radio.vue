<template>
  <el-radio-group 
    v-model="formModel[config.prop]"
    @input="(e) => handleEvent(e, config.input)">
    <template v-if="config.type === 'radioButton'">
      <el-radio-button 
        v-for="(option, index) in config.options" 
        :key="index"
        :disabled="config.disabled"
        :border="config.border"
        :size="config.size"
        :label="option.value"
        >
        {{ option.label }}
      </el-radio-button>  
    </template>
    <template v-else>
      <el-radio 
        v-for="(option, index) in config.options" 
        :key="index"
        :disabled="config.disabled"
        :border="config.border"
        :size="config.size"
        :label="option.value"
        >
        {{ option.label }}
      </el-radio>  
    </template>
  </el-radio-group>
</template>

<script>
  export default {
    name: "Radio",
    props: {
      config: Object,
      formModel: Object
    },
    methods: {
      handleEvent(value, formEvent) {
        if(formEvent) {
          return formEvent(value)
        }
      }
    },
  }
</script>