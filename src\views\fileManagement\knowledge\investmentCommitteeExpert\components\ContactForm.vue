<template>
  <el-form ref="form" :model="queryForm" label-width="110px" :rules="rules">
    <el-row>
      <el-col :span="24">
        <el-form-item label="单位" prop="ttelDept">
          <el-input v-model="queryForm.ttelDept" maxlength="100" show-word-limit  placeholder="请输入单位"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="联系人姓名" prop="ttelLinkName">
          <el-input v-model="queryForm.ttelLinkName" maxlength="100" show-word-limit  placeholder="请输入联系人姓名"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="联系方式" prop="ttelLinkPhone">
          <el-input v-model="queryForm.ttelLinkPhone" maxlength="100" show-word-limit  placeholder="请输入手机号或固定电话"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
  export default {
    name: 'InsideMarketForm',
    props: {
      queryForm: {
        type: Object,
        default: () => {
          return {}
        }
      },
    },
    data(){
      return {
        rules: {
          ttelDept: [
            { required: true, message: '此项为必填项', trigger: 'change' },
          ],
          ttelLinkName: [
            { required: true, message: '此项为必填项', trigger: 'change' },
          ],
          ttelLinkPhone: [
            { required: true, message: '此项为必填项', trigger: 'change' },
            // 正则校验：支持手机号(11位数字)或固定电话(带区号的格式)
            { 
              pattern: /^1[3-9]\d{9}$|^0\d{2,3}-\d{7,8}$/, 
              message: '请输入正确的手机号或固定电话', 
              trigger: 'blur' 
            }
          ],
        },
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>