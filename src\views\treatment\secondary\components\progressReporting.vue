<!--进展填报弹窗-->
<template>
  <BaseDialog
    title="进展填报"
    :visible.sync="dialogVisible"
    type="edit"
    size="Small"
    :close-on-click-modal="false"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="progress-reporting-container">
      <el-form ref="reportingForm" :model="formData" label-width="100px" class="reporting-form">
        <!-- 是否完成 -->
        <el-form-item label="是否完成" prop="isCompleted" required>
          <el-select
            v-model="formData.isCompleted"
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option label="是" value="是"></el-option>
            <el-option label="否" value="否"></el-option>
            <el-option label="部分完成" value="部分完成"></el-option>
          </el-select>
        </el-form-item>

        <!-- 进展情况 -->
        <el-form-item label="进展情况" prop="progressStatus" required>
          <el-input
            v-model="formData.progressStatus"
            type="textarea"
            :rows="6"
            placeholder="请输入进展情况"
            maxlength="500"
            show-word-limit
            resize="none"
          />
        </el-form-item>

        <!-- 上传证明材料 -->
        <el-form-item label="上传证明材料" prop="attachments">
          <div class="upload-section">
            <el-button type="primary" @click="handleUpload">上传</el-button>
            <div v-if="formData.attachments.length > 0" class="file-list">
              <div
                v-for="(file, index) in formData.attachments"
                :key="index"
                class="file-item"
              >
                <span class="file-name" @click="handleViewFile(file)">{{ file.name }}</span>
                <el-button
                  type="text"
                  size="mini"
                  @click="handleRemoveFile(index)"
                  class="remove-btn"
                >
                  删除
                </el-button>
              </div>
            </div>
            <span v-else class="file-placeholder">xxx文件</span>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </BaseDialog>
</template>

<script>
import BaseDialog from '@/components/BaseDialog/index.vue'

export default {
  name: "progressReporting",
  components: {
    BaseDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 初始数据
    initialData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formData: {
        isCompleted: '',
        progressStatus: '',
        attachments: []
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initFormData()
      }
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      this.formData = {
        isCompleted: this.initialData.isCompleted || '',
        progressStatus: this.initialData.progressStatus || '',
        attachments: this.initialData.attachments || []
      }
    },

    // 上传文件
    handleUpload() {
      this.$emit('upload-file')
      // 模拟文件上传
      const newFile = {
        name: `证明材料${this.formData.attachments.length + 1}.pdf`,
        url: '#',
        size: '1.2MB',
        uploadTime: new Date().toLocaleString()
      }
      this.formData.attachments.push(newFile)
      this.$message.success('文件上传成功')
    },

    // 查看文件
    handleViewFile(file) {
      this.$emit('view-file', file)
      this.$message.info(`查看文件：${file.name}`)
    },

    // 删除文件
    handleRemoveFile(index) {
      this.$confirm('确定要删除这个文件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.formData.attachments.splice(index, 1)
        this.$message.success('文件删除成功')
      }).catch(() => {
        // 用户取消删除
      })
    },

    // 确认提交
    handleConfirm() {
      this.$refs.reportingForm.validate((valid) => {
        if (valid) {
          // 验证必填项
          if (!this.formData.isCompleted) {
            this.$message.warning('请选择是否完成')
            return
          }

          if (!this.formData.progressStatus.trim()) {
            this.$message.warning('请输入进展情况')
            return
          }

          this.$emit('confirm', { ...this.formData })
          this.dialogVisible = false
        }
      })
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.progress-reporting-container {
  padding: 20px 0;
}

.reporting-form .el-form-item {
  margin-bottom: 20px;
}

.reporting-form .el-form-item:last-child {
  margin-bottom: 0;
}

.reporting-form .el-textarea__inner {
  font-family: inherit;
  line-height: 1.5;
}

.reporting-form .el-select {
  width: 100%;
}

/* 上传区域样式 */
.upload-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 10px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.file-name {
  color: #409eff;
  cursor: pointer;
  flex: 1;
  font-size: 14px;
}

.file-name:hover {
  text-decoration: underline;
}

.remove-btn {
  color: #f56c6c;
  padding: 0;
  margin-left: 10px;
}

.file-placeholder {
  color: #909399;
  font-size: 14px;
  margin-top: 10px;
}

/* 选择框样式 */
.reporting-form .el-select .el-input__inner {
  height: 36px;
  line-height: 36px;
}

/* 表单项标签样式 */
.reporting-form .el-form-item__label {
  color: #606266;
  font-weight: bold;
}

/* 必填项标识 */
.reporting-form .el-form-item.is-required .el-form-item__label::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .file-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .remove-btn {
    margin-left: 0;
    align-self: flex-end;
  }
}
</style>
