<template>
  <el-dialog
    :append-to-body="flag"
    :close-on-click-modal="false"
    custom-class="commonDialog"
    :show-close="false"
    :visible="dialogTableVisible"
    :width="width"
    :top="top"
  >
    <CardBox :title="title" :height="height">
      <span slot="rightTitle">
        <slot name="dialogRight"></slot>
        <img
          alt=""
          src="@/assets/common/close-fill-right.png"
          :style="{ cursor: 'pointer' }"
          @click="close"
        />
      </span>
      <slot name="content"></slot>
    </CardBox>
    <span v-if="isShowFooter" slot="footer" class="dialog-footer">
      <slot name="footer"></slot>
    </span>
  </el-dialog>
</template>
<script>
  import CardBox from './CardBox'

  export default {
    name: 'DialogCard',
    components: {
      CardBox,
    },
    props: {
      dialogTableVisible: {
        type: Boolean,
        default: false,
      },
      title: {
        type: String,
        default: '',
      },
      close: {
        type: Function,
        default: () => {},
      },
      flag: {
        type: Boolean,
        default: false,
      },
      width: {
        type: String,
        default: '80%',
      },
      top: {
        type: String,
        default: '15vh',
      },
      height: {
        type: String,
        default: '600px',
      },
    },
    data() {
      return {
        isShowFooter: true
      }
    },
    mounted() {
      if (this.$slots['footer']) {
        this.isShowFooter = true
      }else {
        this.isShowFooter = false
      }
    },
  }
</script>

<style lang="scss" scoped>
  :deep() {
    .el-dialog.commonDialog .el-dialog__header {
      display: none;
    }
    .el-dialog.commonDialog .el-dialog__body {
      padding: 0;
    }
    .el-dialog {
      margin-bottom: 0px;
    }
  }
</style>
