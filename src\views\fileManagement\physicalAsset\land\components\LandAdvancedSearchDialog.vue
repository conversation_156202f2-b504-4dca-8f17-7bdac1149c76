<!--土地高级查询弹窗组件-->
<template>
  <base-dialog title="高级查询" type="edit" :visible.sync="dialogVisible" size="Max" @confirm="handleConfirm" @cancel="handleCancel">
    <el-form ref="searchForm" :model="searchForm" label-width="120px" class="advanced-search-form">
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-title">基本信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="资产编号">
              <el-input v-model="searchForm.zcliAssetsNo" placeholder="请输入资产编号" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="土地权属证号">
              <el-input v-model="searchForm.zcliCertificateCode" placeholder="请输入土地权属证明编号" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="现状/用途">
              <el-input v-model="searchForm.zcliUseDescribe" placeholder="请输入现状/用途" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="产权单位">
              <el-input v-model="searchForm.companyName" placeholder="请输入产权单位" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="具体土地位置">
              <el-input v-model="searchForm.zcliAddress" placeholder="请输入具体土地位置" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="取得方式">
              <el-input v-model="searchForm.zcliType" placeholder="请输入取得方式" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="登记时间">
              <el-input v-model="searchForm.zcliDate" placeholder="请输入登记时间" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="境内/境外">
              <el-input v-model="searchForm.zcliRange" placeholder="请输入境内/境外" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否两非资产">
              <el-input v-model="searchForm.zcliIfAssets" placeholder="请输入是否两非资产" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否取得土地权属证明">
              <el-input v-model="searchForm.zcliIfExist" placeholder="请输入是否取得土地权属证明" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否存在纠纷">
              <el-input v-model="searchForm.zcliIfDispute" placeholder="请输入是否存在纠纷" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否可处置">
              <el-input v-model="searchForm.zcliIfDispose" placeholder="请输入是否可处置" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否已抵押">
              <el-input v-model="searchForm.zcliIfMortgage" placeholder="请输入是否已抵押" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 数值信息 -->
      <div class="form-section">
        <div class="section-title">数值信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="土地总面积(㎡)">
              <el-input v-model="searchForm.zcliArea" placeholder="请输入土地总面积" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="原值(万元)">
              <el-input v-model="searchForm.zcliBookValue" placeholder="请输入原值" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="净值(万元)">
              <el-input v-model="searchForm.zcliNetbookValue" placeholder="请输入净值" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="本年计提折旧(万元)">
              <el-input v-model="searchForm.zcliTotalDepreciation" placeholder="请输入本年计提折旧" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最近评估价值(万元)">
              <el-input v-model="searchForm.zcliEvaluateValue" placeholder="请输入最近评估价值" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最近评估日期">
              <el-input v-model="searchForm.zcliEvaluateDate" placeholder="请输入最近评估日期" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="土地使用年限(年)">
              <el-input v-model="searchForm.zcliServiceLife" placeholder="请输入土地使用年限" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="折旧年限">
              <el-input v-model="searchForm.zcliDepreciableYear" placeholder="请输入折旧年限" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 地区信息 -->
      <div class="form-section">
        <div class="section-title">地区信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="省份">
              <el-input v-model="searchForm.zcliProvince" placeholder="请输入省份" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="城市">
              <el-input v-model="searchForm.zcliCity" placeholder="请输入城市" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="区/县">
              <el-input v-model="searchForm.zcliCounty" placeholder="请输入区/县" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 联系信息 -->
      <div class="form-section">
        <div class="section-title">联系信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="经办人">
              <el-input v-model="searchForm.zcliOperator" placeholder="请输入经办人" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="经办人联系方式">
              <el-input v-model="searchForm.zcliOperatorTel" placeholder="请输入经办人联系方式" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="业务主管部门">
              <el-input v-model="searchForm.zcliDeptName" placeholder="请输入业务主管部门" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="部门负责人">
              <el-input v-model="searchForm.zcliDepartmentLeader" placeholder="请输入部门负责人" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="部门负责人联系方式">
              <el-input v-model="searchForm.zcliDepartmentTel" placeholder="请输入部门负责人联系方式" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="分管所(公司)领导">
              <el-input v-model="searchForm.zcliCompanyLeader" placeholder="请输入分管所(公司)领导" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="分管所(公司)领导联系方式">
              <el-input v-model="searchForm.zcliCompanyTel" placeholder="请输入分管所(公司)领导联系方式" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 其他信息 -->
      <div class="form-section">
        <div class="section-title">其他信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="备注">
              <el-input v-model="searchForm.zcliRemark" placeholder="请输入备注" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建人">
              <el-input v-model="searchForm.createdBy" placeholder="请输入创建人" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleReset">重置</el-button>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">查询</el-button>
      </div>
    </template>
  </base-dialog>
</template>

<script>
import BaseDialog from '@/components/BaseDialog/index.vue'

export default {
  name: 'LandAdvancedSearchDialog',
  components: {
    BaseDialog
  },
  props: {
    // 外部传入的查询条件
    externalSearchForm: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      dialogVisible: false,
      searchForm: {
        zcliAssetsNo: '',
        zcliCertificateCode: '',
        zcliUseDescribe: '',
        companyName: '',
        zcliAddress: '',
        zcliType: '',
        zcliDate: '',
        zcliRange: '',
        zcliIfAssets: '',
        zcliIfExist: '',
        zcliIfDispute: '',
        zcliIfDispose: '',
        zcliIfMortgage: '',
        zcliArea: '',
        zcliBookValue: '',
        zcliNetbookValue: '',
        zcliTotalDepreciation: '',
        zcliEvaluateValue: '',
        zcliEvaluateDate: '',
        zcliServiceLife: '',
        zcliDepreciableYear: '',
        zcliProvince: '',
        zcliCity: '',
        zcliCounty: '',
        zcliOperator: '',
        zcliOperatorTel: '',
        zcliDeptName: '',
        zcliDepartmentLeader: '',
        zcliDepartmentTel: '',
        zcliCompanyLeader: '',
        zcliCompanyTel: '',
        zcliRemark: '',
        createdBy: ''
      }
    }
  },
  methods: {
    // 显示弹窗
    showDialog () {
      // 同步外部查询条件到弹窗表单
      this.syncExternalSearchForm()
      this.dialogVisible = true
    },

    // 同步外部查询条件
    syncExternalSearchForm () {
      // 将外部查询条件同步到弹窗表单中
      Object.keys(this.searchForm).forEach(key => {
        if (this.externalSearchForm[key] !== undefined) {
          this.searchForm[key] = this.externalSearchForm[key]
        }
      })
    },

    // 关闭弹窗
    closeDialog () {
      this.dialogVisible = false
    },

    // 确认查询
    handleConfirm () {
      // 创建完整的查询参数对象，包含所有字段（空值也保留，用于清空外部对应字段）
      const searchParams = { ...this.searchForm }

      // 清理字符串字段的前后空格
      Object.keys(searchParams).forEach(key => {
        if (typeof searchParams[key] === 'string') {
          searchParams[key] = searchParams[key].trim()
        }
      })

      this.$emit('search', searchParams)
      this.closeDialog()
    },

    // 取消
    handleCancel () {
      this.closeDialog()
    },

    // 重置表单
    handleReset () {
      this.$refs.searchForm.resetFields()
      // 手动重置所有字段
      Object.keys(this.searchForm).forEach(key => {
        this.searchForm[key] = ''
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.advanced-search-form {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 10px;
}

.form-section {
  margin-bottom: 30px;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #409eff;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      bottom: -2px;
      width: 30px;
      height: 2px;
      background-color: #409eff;
    }
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
}
</style>
