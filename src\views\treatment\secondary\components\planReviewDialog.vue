<!--审核计划/报送集团弹窗-->
<template>
  <BaseDialog
    title="审核计划/报送集团"
    :visible.sync="dialogVisible"
    type="edit"
    size="Small"
    :close-on-click-modal="false"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="plan-review-container">
      <el-form ref="reviewForm" :model="formData" label-width="100px" class="review-form">
        <!-- 查看计划按钮 -->
        <el-form-item label="">
          <el-button type="primary" @click="handleViewPlan" class="view-plan-btn">
            查看计划
          </el-button>
        </el-form-item>

        <!-- 领导审核 -->
        <el-form-item label="领导审核" prop="leaderReview" required>
          <el-select
            v-model="formData.leaderReview"
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option label="同意" value="同意"></el-option>
            <el-option label="不同意" value="不同意"></el-option>
          </el-select>
        </el-form-item>

        <!-- 领导审核意见 -->
        <el-form-item label="领导审核意见" prop="reviewOpinion" required>
          <el-input
            v-model="formData.reviewOpinion"
            type="textarea"
            :rows="6"
            placeholder="请输入领导审核意见"
            maxlength="500"
            show-word-limit
            resize="none"
          />
        </el-form-item>
      </el-form>
    </div>
  </BaseDialog>
</template>

<script>
import BaseDialog from '@/components/BaseDialog/index.vue'

export default {
  name: "planReviewDialog",
  components: {
    BaseDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 初始数据
    initialData: {
      type: Object,
      default: () => ({})
    },
    // 计划数据
    planData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formData: {
        leaderReview: '',
        reviewOpinion: ''
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initFormData()
      }
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      this.formData = {
        leaderReview: this.initialData.leaderReview || '',
        reviewOpinion: this.initialData.reviewOpinion || ''
      }
    },

    // 查看计划
    handleViewPlan() {
      this.$emit('view-plan', this.planData)
      this.$message.info('查看计划详情')
    },

    // 确认提交
    handleConfirm() {
      this.$refs.reviewForm.validate((valid) => {
        if (valid) {
          // 验证必填项
          if (!this.formData.leaderReview) {
            this.$message.warning('请选择领导审核结果')
            return
          }

          if (!this.formData.reviewOpinion.trim()) {
            this.$message.warning('请输入领导审核意见')
            return
          }

          this.$emit('confirm', { ...this.formData })
          this.dialogVisible = false
        }
      })
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.plan-review-container {
  padding: 20px 0;
}

.review-form .el-form-item {
  margin-bottom: 20px;
}

.review-form .el-form-item:last-child {
  margin-bottom: 0;
}

.review-form .el-textarea__inner {
  font-family: inherit;
  line-height: 1.5;
}

.review-form .el-select {
  width: 100%;
}

/* 查看计划按钮样式 */
.view-plan-btn {
  width: 120px;
  height: 36px;
  font-size: 14px;
}

/* 选择框样式 */
.review-form .el-select .el-input__inner {
  height: 36px;
  line-height: 36px;
}

/* 表单项标签样式 */
.review-form .el-form-item__label {
  color: #606266;
  font-weight: bold;
}

/* 必填项标识 */
.review-form .el-form-item.is-required .el-form-item__label::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .view-plan-btn {
    width: 100%;
  }
}
</style>
