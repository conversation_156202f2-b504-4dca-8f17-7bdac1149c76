<template>
  <el-input-number  
    v-model="formModel[config.prop]"
    :type="config.type"
    :placeholder="config.placeholder"
    :clearable="config.clearable"
    :readonly="config.readonly"
    :min="config.min"
    :max="config.max"
    :step="config.step"
    :precision="config.precision"
    :size="config.size"
    :controls-position="config.controlsPosition"
    :style="config.style"
    @change="(e) => handleEvent(e, config.change)"
    @blur="(e) => handleEvent(e, config.blur)"
    @focus="(e) => handleEvent(e, config.focus)" />
</template>

<script>
  export default {
    name: "InputNumber",
    props: {
      config: Object,
      formModel: Object
    },
    methods: {
      handleEvent(value, formEvent) {
        if(formEvent) {
          return formEvent(value)
        }
      }
    }
  }
</script>