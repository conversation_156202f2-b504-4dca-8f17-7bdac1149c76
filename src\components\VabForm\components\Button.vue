<template>
  <el-button 
    v-model="formModel[config.prop]"
    :type="config.type"
    :readonly="config.readonly"
    :size="config.size"
    :round="config.round"
    :disabled="config.disabled"
    :icon="config.icon"
    :style="config.style"
    @click="(e) => handleEvent(e, config.click)">
    {{ config.label }}
  </el-button>
</template>

<script>
  export default {
    name: "Button",
    props: {
      config: Object,
      formModel: Object
    },
    methods: {
      handleEvent(value, formEvent) {
        if(formEvent) {
          return formEvent(value)
        }
      }
    }
  }
</script>