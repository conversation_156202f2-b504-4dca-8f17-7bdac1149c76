<template>
  <el-checkbox-group 
    v-model="formModel[config.prop]" 
    @input="(e) => handleEvent(e, config.input)">
    <template v-if="config.type === 'checkboxButton'">
      <el-checkbox-button
        v-for="(option, index) in config.options" 
        :key="index"
        :disabled="config.disabled"
        :border="config.border"
        :size="config.size"
        :label="option.value"
        >
        {{ option.label }}
      </el-checkbox-button>  
    </template>
    <template v-else>
      <el-checkbox 
        v-for="(option, index) in config.options" 
        :key="index"
        :disabled="config.disabled"
        :border="config.border"
        :size="config.size"
        :label="option.value"
        >
        {{ option.label }}
      </el-checkbox>  
    </template>
  </el-checkbox-group>
</template>

<script>
  export default {
    name: "Checkbox",
    props: {
      config: Object,
      formModel: Object
    },
    methods: {
      handleEvent(value, formEvent) {
        if(formEvent) {
          return formEvent(value)
        }
      }
    },
  }
</script>