<template>
  <el-form ref="form" :model="queryForm" label-width="110px" :rules="rules">
     <el-row>
      <el-col :span="12">
        
      </el-col>
    </el-row>
  </el-form>
</template>

<script>

  export default {
    name: 'InsideMarketForm',
    props: {
      queryForm: {
        type: Object,
        default: () => {
          return {}
        }
      },
    },
    data(){
      return {
        rules: {
          ztjType: [
            { required: true, message: '此项为必填项', trigger: 'blur' },
          ],
        },
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>